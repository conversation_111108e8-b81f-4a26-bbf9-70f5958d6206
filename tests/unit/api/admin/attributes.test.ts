import { NextRequest } from "next/server";
import { GET, POST } from "@/app/api/admin/attributes/route";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";

// Mock dependencies
jest.mock("@/lib/prisma", () => ({
  prisma: {
    attribute: {
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
      findUnique: jest.fn(),
    },
    attributeValue: {
      createMany: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

jest.mock("@/lib/admin-auth", () => ({
  authOptions: {},
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe("/api/admin/attributes", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("GET", () => {
    it("should return 401 when not authenticated", async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest("http://localhost:3000/api/admin/attributes");
      const response = await GET(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe("Không có quyền truy cập");
    });

    it("should return 401 when user is not admin", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { role: "USER" },
      } as any);

      const request = new NextRequest("http://localhost:3000/api/admin/attributes");
      const response = await GET(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe("Không có quyền truy cập");
    });

    it("should return attributes with pagination", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { role: "ADMIN" },
      } as any);

      const mockAttributes = [
        {
          id: "1",
          name: "Màu sắc",
          slug: "mau-sac",
          type: "COLOR",
          isRequired: false,
          isFilterable: true,
          sortOrder: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
          _count: { values: 5, products: 10 },
        },
      ];

      mockPrisma.attribute.findMany.mockResolvedValue(mockAttributes);
      mockPrisma.attribute.count.mockResolvedValue(1);

      const request = new NextRequest("http://localhost:3000/api/admin/attributes?page=1&limit=20");
      const response = await GET(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.data).toEqual(mockAttributes);
      expect(data.pagination).toEqual({
        page: 1,
        limit: 20,
        total: 1,
        pages: 1,
      });
    });

    it("should handle search filter", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { role: "ADMIN" },
      } as any);

      mockPrisma.attribute.findMany.mockResolvedValue([]);
      mockPrisma.attribute.count.mockResolvedValue(0);

      const request = new NextRequest("http://localhost:3000/api/admin/attributes?search=màu");
      await GET(request);

      expect(mockPrisma.attribute.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              { name: { contains: "màu", mode: "insensitive" } },
              { slug: { contains: "màu", mode: "insensitive" } },
              { description: { contains: "màu", mode: "insensitive" } },
            ]),
          }),
        })
      );
    });

    it("should handle type filter", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { role: "ADMIN" },
      } as any);

      mockPrisma.attribute.findMany.mockResolvedValue([]);
      mockPrisma.attribute.count.mockResolvedValue(0);

      const request = new NextRequest("http://localhost:3000/api/admin/attributes?type=COLOR");
      await GET(request);

      expect(mockPrisma.attribute.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            type: "COLOR",
          }),
        })
      );
    });

    it("should handle sorting", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { role: "ADMIN" },
      } as any);

      mockPrisma.attribute.findMany.mockResolvedValue([]);
      mockPrisma.attribute.count.mockResolvedValue(0);

      const request = new NextRequest("http://localhost:3000/api/admin/attributes?sortBy=name&sortOrder=desc");
      await GET(request);

      expect(mockPrisma.attribute.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBy: { name: "desc" },
        })
      );
    });
  });

  describe("POST", () => {
    it("should return 401 when not authenticated", async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify({
          name: "Test Attribute",
          type: "TEXT",
        }),
      });

      const response = await POST(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe("Không có quyền truy cập");
    });

    it("should create attribute without values", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { role: "ADMIN" },
      } as any);

      const attributeData = {
        name: "Mô tả",
        type: "TEXT",
        isRequired: false,
        isFilterable: false,
        sortOrder: 0,
      };

      const mockCreatedAttribute = {
        id: "1",
        ...attributeData,
        slug: "mo-ta",
        description: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.attribute.findUnique.mockResolvedValue(null);
      mockPrisma.attribute.create.mockResolvedValue(mockCreatedAttribute);

      const request = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(attributeData),
      });

      const response = await POST(request);

      expect(response.status).toBe(201);
      const data = await response.json();
      expect(data.name).toBe(attributeData.name);
      expect(data.slug).toBe("mo-ta");
    });

    it("should create attribute with values", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { role: "ADMIN" },
      } as any);

      const attributeData = {
        name: "Màu sắc",
        type: "COLOR",
        isRequired: false,
        isFilterable: true,
        sortOrder: 1,
        values: [
          { value: "Đỏ", slug: "do", sortOrder: 1 },
          { value: "Xanh", slug: "xanh", sortOrder: 2 },
        ],
      };

      const mockCreatedAttribute = {
        id: "1",
        name: attributeData.name,
        slug: "mau-sac",
        type: attributeData.type,
        isRequired: attributeData.isRequired,
        isFilterable: attributeData.isFilterable,
        sortOrder: attributeData.sortOrder,
        description: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.attribute.findUnique.mockResolvedValue(null);
      mockPrisma.$transaction.mockResolvedValue(mockCreatedAttribute);

      const request = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(attributeData),
      });

      const response = await POST(request);

      expect(response.status).toBe(201);
      expect(mockPrisma.$transaction).toHaveBeenCalled();
    });

    it("should return 400 for duplicate slug", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { role: "ADMIN" },
      } as any);

      const attributeData = {
        name: "Màu sắc",
        slug: "mau-sac",
        type: "COLOR",
      };

      mockPrisma.attribute.findUnique.mockResolvedValue({
        id: "existing",
        slug: "mau-sac",
      } as any);

      const request = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(attributeData),
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBe("Slug đã tồn tại");
    });

    it("should return 400 for invalid data", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { role: "ADMIN" },
      } as any);

      const invalidData = {
        // Missing required name field
        type: "TEXT",
      };

      const request = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(invalidData),
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBe("Dữ liệu không hợp lệ");
    });

    it("should auto-generate slug when not provided", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { role: "ADMIN" },
      } as any);

      const attributeData = {
        name: "Màu sắc đặc biệt",
        type: "COLOR",
      };

      const mockCreatedAttribute = {
        id: "1",
        ...attributeData,
        slug: "mau-sac-dac-biet",
        isRequired: false,
        isFilterable: true,
        sortOrder: 0,
        description: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.attribute.findUnique.mockResolvedValue(null);
      mockPrisma.attribute.create.mockResolvedValue(mockCreatedAttribute);

      const request = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(attributeData),
      });

      const response = await POST(request);

      expect(response.status).toBe(201);
      const data = await response.json();
      expect(data.slug).toBe("mau-sac-dac-biet");
    });

    it("should handle database errors", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { role: "ADMIN" },
      } as any);

      const attributeData = {
        name: "Test Attribute",
        type: "TEXT",
      };

      mockPrisma.attribute.findUnique.mockResolvedValue(null);
      mockPrisma.attribute.create.mockRejectedValue(new Error("Database error"));

      const request = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(attributeData),
      });

      const response = await POST(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.error).toBe("Có lỗi xảy ra khi tạo thuộc tính");
    });
  });
});
