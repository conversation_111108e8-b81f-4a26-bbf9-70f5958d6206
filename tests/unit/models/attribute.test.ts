import { prisma } from "@/lib/prisma";
import { AttributeType } from "@/types/attribute";

describe("Attribute Model", () => {
  beforeEach(async () => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
    await prisma.$disconnect();
  });

  describe("Creation", () => {
    it("should create an attribute with required fields", async () => {
      const attributeData = {
        name: "<PERSON><PERSON>u sắc",
        slug: "mau-sac",
        type: "COLOR" as AttributeType,
        isRequired: false,
        isFilterable: true,
        sortOrder: 1,
      };

      const attribute = await prisma.attribute.create({
        data: attributeData,
      });

      expect(attribute).toBeDefined();
      expect(attribute.name).toBe(attributeData.name);
      expect(attribute.slug).toBe(attributeData.slug);
      expect(attribute.type).toBe(attributeData.type);
      expect(attribute.isRequired).toBe(attributeData.isRequired);
      expect(attribute.isFilterable).toBe(attributeData.isFilterable);
      expect(attribute.sortOrder).toBe(attributeData.sortOrder);
      expect(attribute.id).toBeDefined();
      expect(attribute.createdAt).toBeDefined();
      expect(attribute.updatedAt).toBeDefined();
    });

    it("should create an attribute with optional description", async () => {
      const attributeData = {
        name: "Kích thước",
        slug: "kich-thuoc",
        description: "Kích thước sản phẩm",
        type: "SIZE" as AttributeType,
        isRequired: true,
        isFilterable: true,
        sortOrder: 2,
      };

      const attribute = await prisma.attribute.create({
        data: attributeData,
      });

      expect(attribute.description).toBe(attributeData.description);
    });

    it("should use default values for optional fields", async () => {
      const attributeData = {
        name: "Mô tả",
        slug: "mo-ta",
        type: "TEXT" as AttributeType,
      };

      const attribute = await prisma.attribute.create({
        data: attributeData,
      });

      expect(attribute.isRequired).toBe(false); // Default value
      expect(attribute.isFilterable).toBe(true); // Default value
      expect(attribute.sortOrder).toBe(0); // Default value
    });

    it("should enforce unique slug constraint", async () => {
      const attributeData = {
        name: "Test Attribute",
        slug: "test-attribute",
        type: "TEXT" as AttributeType,
      };

      // Create first attribute
      await prisma.attribute.create({
        data: attributeData,
      });

      // Try to create second attribute with same slug
      await expect(
        prisma.attribute.create({
          data: {
            ...attributeData,
            name: "Another Test Attribute",
          },
        })
      ).rejects.toThrow();
    });
  });

  describe("Relationships", () => {
    it("should create attribute with values", async () => {
      const attribute = await prisma.attribute.create({
        data: {
          name: "Màu sắc",
          slug: "mau-sac",
          type: "COLOR" as AttributeType,
          isRequired: false,
          isFilterable: true,
          sortOrder: 1,
        },
      });

      const values = await prisma.attributeValue.createMany({
        data: [
          {
            attributeId: attribute.id,
            value: "Đỏ",
            slug: "do",
            sortOrder: 1,
          },
          {
            attributeId: attribute.id,
            value: "Xanh",
            slug: "xanh",
            sortOrder: 2,
          },
        ],
      });

      expect(values.count).toBe(2);

      // Fetch attribute with values
      const attributeWithValues = await prisma.attribute.findUnique({
        where: { id: attribute.id },
        include: { values: true },
      });

      expect(attributeWithValues?.values).toHaveLength(2);
      expect(attributeWithValues?.values[0].value).toBe("Đỏ");
      expect(attributeWithValues?.values[1].value).toBe("Xanh");
    });

    it("should cascade delete values when attribute is deleted", async () => {
      const attribute = await prisma.attribute.create({
        data: {
          name: "Test Attribute",
          slug: "test-attribute",
          type: "SELECT" as AttributeType,
        },
      });

      await prisma.attributeValue.create({
        data: {
          attributeId: attribute.id,
          value: "Test Value",
          slug: "test-value",
          sortOrder: 1,
        },
      });

      // Delete attribute
      await prisma.attribute.delete({
        where: { id: attribute.id },
      });

      // Check that values are also deleted
      const remainingValues = await prisma.attributeValue.findMany({
        where: { attributeId: attribute.id },
      });

      expect(remainingValues).toHaveLength(0);
    });
  });

  describe("Queries", () => {
    beforeEach(async () => {
      // Create test data
      const colorAttribute = await prisma.attribute.create({
        data: {
          name: "Màu sắc",
          slug: "mau-sac",
          type: "COLOR" as AttributeType,
          isRequired: false,
          isFilterable: true,
          sortOrder: 1,
        },
      });

      const sizeAttribute = await prisma.attribute.create({
        data: {
          name: "Kích thước",
          slug: "kich-thuoc",
          type: "SIZE" as AttributeType,
          isRequired: true,
          isFilterable: true,
          sortOrder: 2,
        },
      });

      await prisma.attributeValue.createMany({
        data: [
          {
            attributeId: colorAttribute.id,
            value: "Đỏ",
            slug: "do",
            sortOrder: 1,
          },
          {
            attributeId: sizeAttribute.id,
            value: "M",
            slug: "m",
            sortOrder: 1,
          },
        ],
      });
    });

    it("should find attributes by type", async () => {
      const colorAttributes = await prisma.attribute.findMany({
        where: { type: "COLOR" },
      });

      expect(colorAttributes).toHaveLength(1);
      expect(colorAttributes[0].name).toBe("Màu sắc");
    });

    it("should find required attributes", async () => {
      const requiredAttributes = await prisma.attribute.findMany({
        where: { isRequired: true },
      });

      expect(requiredAttributes).toHaveLength(1);
      expect(requiredAttributes[0].name).toBe("Kích thước");
    });

    it("should find filterable attributes", async () => {
      const filterableAttributes = await prisma.attribute.findMany({
        where: { isFilterable: true },
      });

      expect(filterableAttributes).toHaveLength(2);
    });

    it("should order attributes by sortOrder", async () => {
      const attributes = await prisma.attribute.findMany({
        orderBy: { sortOrder: "asc" },
      });

      expect(attributes).toHaveLength(2);
      expect(attributes[0].name).toBe("Màu sắc");
      expect(attributes[1].name).toBe("Kích thước");
    });

    it("should include value counts", async () => {
      const attributes = await prisma.attribute.findMany({
        include: {
          _count: {
            select: {
              values: true,
              products: true,
            },
          },
        },
      });

      expect(attributes).toHaveLength(2);
      expect(attributes[0]._count.values).toBe(1);
      expect(attributes[1]._count.values).toBe(1);
    });

    it("should search attributes by name", async () => {
      const attributes = await prisma.attribute.findMany({
        where: {
          name: {
            contains: "màu",
            mode: "insensitive",
          },
        },
      });

      expect(attributes).toHaveLength(1);
      expect(attributes[0].name).toBe("Màu sắc");
    });
  });

  describe("Updates", () => {
    it("should update attribute fields", async () => {
      const attribute = await prisma.attribute.create({
        data: {
          name: "Test Attribute",
          slug: "test-attribute",
          type: "TEXT" as AttributeType,
          isRequired: false,
          isFilterable: false,
          sortOrder: 0,
        },
      });

      const updatedAttribute = await prisma.attribute.update({
        where: { id: attribute.id },
        data: {
          name: "Updated Attribute",
          isRequired: true,
          isFilterable: true,
          sortOrder: 5,
        },
      });

      expect(updatedAttribute.name).toBe("Updated Attribute");
      expect(updatedAttribute.isRequired).toBe(true);
      expect(updatedAttribute.isFilterable).toBe(true);
      expect(updatedAttribute.sortOrder).toBe(5);
      expect(updatedAttribute.updatedAt.getTime()).toBeGreaterThan(
        attribute.updatedAt.getTime()
      );
    });

    it("should update attribute description", async () => {
      const attribute = await prisma.attribute.create({
        data: {
          name: "Test Attribute",
          slug: "test-attribute",
          type: "TEXT" as AttributeType,
        },
      });

      const updatedAttribute = await prisma.attribute.update({
        where: { id: attribute.id },
        data: {
          description: "Updated description",
        },
      });

      expect(updatedAttribute.description).toBe("Updated description");
    });
  });
});
