import {
  generateSlug,
  needsValues,
  supportsMultipleValues,
  getColorCode,
  validateAttribute,
  sortAttributes,
  sortAttributeValues,
  filterAttributes,
  groupAttributesByType,
  getAttributeStats,
  formatAttributeValue,
  canDeleteAttribute,
  canDeleteAttributeValue,
  validateAttributeSelection,
} from "@/lib/utils/attributes";
import { Attribute, AttributeValue, AttributeType } from "@/types/attribute";

describe("Attribute Utilities", () => {
  describe("generateSlug", () => {
    it("should generate slug from Vietnamese text", () => {
      expect(generateSlug("Màu sắc")).toBe("mau-sac");
      expect(generateSlug("Kích thước")).toBe("kich-thuoc");
      expect(generateSlug("Chất liệu")).toBe("chat-lieu");
    });

    it("should handle special characters", () => {
      expect(generateSlug("<PERSON>o dài truyền thống")).toBe("ao-dai-truyen-thong");
      expect(generateSlug("<PERSON><PERSON> bơi nữ")).toBe("do-boi-nu");
    });

    it("should handle multiple spaces and special characters", () => {
      expect(generateSlug("  Màu   sắc   đặc biệt  ")).toBe("mau-sac-dac-biet");
      expect(generateSlug("Size XL (Extra Large)")).toBe("size-xl-extra-large");
    });

    it("should return empty string for empty input", () => {
      expect(generateSlug("")).toBe("");
      expect(generateSlug("   ")).toBe("");
    });
  });

  describe("needsValues", () => {
    it("should return true for types that need values", () => {
      expect(needsValues("COLOR")).toBe(true);
      expect(needsValues("SIZE")).toBe(true);
      expect(needsValues("SELECT")).toBe(true);
      expect(needsValues("MULTI_SELECT")).toBe(true);
    });

    it("should return false for types that don't need values", () => {
      expect(needsValues("TEXT")).toBe(false);
      expect(needsValues("NUMBER")).toBe(false);
      expect(needsValues("BOOLEAN")).toBe(false);
    });
  });

  describe("supportsMultipleValues", () => {
    it("should return true only for MULTI_SELECT", () => {
      expect(supportsMultipleValues("MULTI_SELECT")).toBe(true);
    });

    it("should return false for other types", () => {
      expect(supportsMultipleValues("SELECT")).toBe(false);
      expect(supportsMultipleValues("COLOR")).toBe(false);
      expect(supportsMultipleValues("TEXT")).toBe(false);
    });
  });

  describe("getColorCode", () => {
    it("should return correct color codes for Vietnamese colors", () => {
      expect(getColorCode("đỏ")).toBe("#ef4444");
      expect(getColorCode("xanh dương")).toBe("#3b82f6");
      expect(getColorCode("xanh lá")).toBe("#22c55e");
      expect(getColorCode("đen")).toBe("#000000");
      expect(getColorCode("trắng")).toBe("#ffffff");
    });

    it("should return correct color codes for English colors", () => {
      expect(getColorCode("red")).toBe("#ef4444");
      expect(getColorCode("blue")).toBe("#3b82f6");
      expect(getColorCode("green")).toBe("#22c55e");
    });

    it("should return default color for unknown colors", () => {
      expect(getColorCode("unknown")).toBe("#6b7280");
      expect(getColorCode("")).toBe("#6b7280");
    });

    it("should be case insensitive", () => {
      expect(getColorCode("ĐỎ")).toBe("#ef4444");
      expect(getColorCode("RED")).toBe("#ef4444");
    });
  });

  describe("validateAttribute", () => {
    it("should validate valid attribute data", () => {
      const validData = {
        name: "Màu sắc",
        slug: "mau-sac",
        type: "COLOR" as AttributeType,
        values: [
          { value: "Đỏ", slug: "do" },
          { value: "Xanh", slug: "xanh" },
        ],
      };

      const result = validateAttribute(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should reject empty name", () => {
      const invalidData = {
        name: "",
        type: "TEXT" as AttributeType,
      };

      const result = validateAttribute(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Tên thuộc tính là bắt buộc");
    });

    it("should reject invalid slug format", () => {
      const invalidData = {
        name: "Test",
        slug: "Invalid Slug!",
        type: "TEXT" as AttributeType,
      };

      const result = validateAttribute(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Slug chỉ được chứa chữ cái thường, số và dấu gạch ngang");
    });

    it("should require values for types that need them", () => {
      const invalidData = {
        name: "Màu sắc",
        type: "COLOR" as AttributeType,
        values: [],
      };

      const result = validateAttribute(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Loại thuộc tính Màu sắc cần ít nhất một giá trị");
    });

    it("should reject duplicate values", () => {
      const invalidData = {
        name: "Màu sắc",
        type: "COLOR" as AttributeType,
        values: [
          { value: "Đỏ", slug: "do" },
          { value: "đỏ", slug: "do-2" },
        ],
      };

      const result = validateAttribute(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Không được có giá trị trùng lặp");
    });

    it("should reject empty values", () => {
      const invalidData = {
        name: "Màu sắc",
        type: "COLOR" as AttributeType,
        values: [
          { value: "Đỏ", slug: "do" },
          { value: "", slug: "empty" },
        ],
      };

      const result = validateAttribute(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Tất cả giá trị phải có nội dung");
    });
  });

  describe("sortAttributes", () => {
    it("should sort by sortOrder first, then by name", () => {
      const attributes: Attribute[] = [
        {
          id: "1",
          name: "Z Attribute",
          slug: "z-attr",
          type: "TEXT",
          sortOrder: 1,
          isRequired: false,
          isFilterable: true,
          createdAt: "2023-01-01",
          updatedAt: "2023-01-01",
        },
        {
          id: "2",
          name: "A Attribute",
          slug: "a-attr",
          type: "TEXT",
          sortOrder: 2,
          isRequired: false,
          isFilterable: true,
          createdAt: "2023-01-01",
          updatedAt: "2023-01-01",
        },
        {
          id: "3",
          name: "B Attribute",
          slug: "b-attr",
          type: "TEXT",
          sortOrder: 1,
          isRequired: false,
          isFilterable: true,
          createdAt: "2023-01-01",
          updatedAt: "2023-01-01",
        },
      ];

      const sorted = sortAttributes(attributes);
      expect(sorted[0].name).toBe("B Attribute");
      expect(sorted[1].name).toBe("Z Attribute");
      expect(sorted[2].name).toBe("A Attribute");
    });
  });

  describe("filterAttributes", () => {
    const attributes: Attribute[] = [
      {
        id: "1",
        name: "Màu sắc",
        slug: "mau-sac",
        type: "COLOR",
        description: "Màu sắc sản phẩm",
        sortOrder: 1,
        isRequired: false,
        isFilterable: true,
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
      },
      {
        id: "2",
        name: "Kích thước",
        slug: "kich-thuoc",
        type: "SIZE",
        description: "Kích thước sản phẩm",
        sortOrder: 2,
        isRequired: true,
        isFilterable: true,
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
      },
    ];

    it("should filter by name", () => {
      const filtered = filterAttributes(attributes, "màu");
      expect(filtered).toHaveLength(1);
      expect(filtered[0].name).toBe("Màu sắc");
    });

    it("should filter by slug", () => {
      const filtered = filterAttributes(attributes, "kich-thuoc");
      expect(filtered).toHaveLength(1);
      expect(filtered[0].slug).toBe("kich-thuoc");
    });

    it("should filter by description", () => {
      const filtered = filterAttributes(attributes, "sản phẩm");
      expect(filtered).toHaveLength(2);
    });

    it("should return all attributes for empty search", () => {
      const filtered = filterAttributes(attributes, "");
      expect(filtered).toHaveLength(2);
    });
  });

  describe("getAttributeStats", () => {
    const attributes: Attribute[] = [
      {
        id: "1",
        name: "Color",
        slug: "color",
        type: "COLOR",
        sortOrder: 1,
        isRequired: true,
        isFilterable: true,
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        _count: { values: 5, products: 10 },
      },
      {
        id: "2",
        name: "Size",
        slug: "size",
        type: "SIZE",
        sortOrder: 2,
        isRequired: false,
        isFilterable: true,
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        _count: { values: 0, products: 0 },
      },
      {
        id: "3",
        name: "Description",
        slug: "description",
        type: "TEXT",
        sortOrder: 3,
        isRequired: false,
        isFilterable: false,
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
      },
    ];

    it("should calculate correct statistics", () => {
      const stats = getAttributeStats(attributes);
      
      expect(stats.total).toBe(3);
      expect(stats.required).toBe(1);
      expect(stats.filterable).toBe(2);
      expect(stats.withValues).toBe(1);
      expect(stats.withoutValues).toBe(2);
      expect(stats.byType.COLOR).toBe(1);
      expect(stats.byType.SIZE).toBe(1);
      expect(stats.byType.TEXT).toBe(1);
    });
  });

  describe("formatAttributeValue", () => {
    const booleanAttribute: Attribute = {
      id: "1",
      name: "Waterproof",
      slug: "waterproof",
      type: "BOOLEAN",
      sortOrder: 1,
      isRequired: false,
      isFilterable: true,
      createdAt: "2023-01-01",
      updatedAt: "2023-01-01",
    };

    it("should format boolean values correctly", () => {
      expect(formatAttributeValue(booleanAttribute, "true")).toBe("Có");
      expect(formatAttributeValue(booleanAttribute, "false")).toBe("Không");
    });

    it("should return value as-is for other types", () => {
      const textAttribute: Attribute = {
        ...booleanAttribute,
        type: "TEXT",
      };

      expect(formatAttributeValue(textAttribute, "Some text")).toBe("Some text");
    });
  });

  describe("canDeleteAttribute", () => {
    it("should allow deletion when not used by products", () => {
      const attribute: Attribute = {
        id: "1",
        name: "Test",
        slug: "test",
        type: "TEXT",
        sortOrder: 1,
        isRequired: false,
        isFilterable: true,
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        _count: { values: 0, products: 0 },
      };

      const result = canDeleteAttribute(attribute);
      expect(result.canDelete).toBe(true);
      expect(result.reason).toBeUndefined();
    });

    it("should prevent deletion when used by products", () => {
      const attribute: Attribute = {
        id: "1",
        name: "Test",
        slug: "test",
        type: "TEXT",
        sortOrder: 1,
        isRequired: false,
        isFilterable: true,
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        _count: { values: 0, products: 5 },
      };

      const result = canDeleteAttribute(attribute);
      expect(result.canDelete).toBe(false);
      expect(result.reason).toBe("Thuộc tính đang được sử dụng bởi 5 sản phẩm");
    });
  });

  describe("validateAttributeSelection", () => {
    const attributes: Attribute[] = [
      {
        id: "1",
        name: "Color",
        slug: "color",
        type: "SELECT",
        sortOrder: 1,
        isRequired: true,
        isFilterable: true,
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
      },
      {
        id: "2",
        name: "Tags",
        slug: "tags",
        type: "MULTI_SELECT",
        sortOrder: 2,
        isRequired: false,
        isFilterable: true,
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
      },
    ];

    it("should validate correct selection", () => {
      const selection = {
        "1": "red",
        "2": ["tag1", "tag2"],
      };

      const result = validateAttributeSelection(attributes, selection);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should require required attributes", () => {
      const selection = {
        "2": ["tag1"],
      };

      const result = validateAttributeSelection(attributes, selection);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Color là thuộc tính bắt buộc");
    });

    it("should validate value types", () => {
      const selection = {
        "1": ["red", "blue"], // Should be single value
        "2": "tag1", // Should be array
      };

      const result = validateAttributeSelection(attributes, selection);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Color chỉ được chọn một giá trị");
      expect(result.errors).toContain("Tags phải là mảng giá trị");
    });
  });
});
