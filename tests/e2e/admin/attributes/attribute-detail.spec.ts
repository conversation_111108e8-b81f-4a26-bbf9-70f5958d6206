import { test, expect } from "@playwright/test";
import { prisma } from "@/lib/prisma";

test.describe("Attribute Detail Page", () => {
  let testAttributeId: string;

  test.beforeEach(async ({ page }) => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();

    // Create test attribute with values
    const testAttribute = await prisma.attribute.create({
      data: {
        name: "<PERSON><PERSON><PERSON> sắc",
        slug: "mau-sac",
        type: "COLOR",
        description: "<PERSON><PERSON><PERSON> sắc sản phẩm",
        isRequired: false,
        isFilterable: true,
        sortOrder: 1,
      },
    });

    testAttributeId = testAttribute.id;

    // Create test values
    await prisma.attributeValue.createMany({
      data: [
        {
          attributeId: testAttributeId,
          value: "Đỏ",
          slug: "do",
          sortOrder: 1,
        },
        {
          attributeId: testAttributeId,
          value: "<PERSON>anh dương",
          slug: "xanh-duong",
          sortOrder: 2,
        },
        {
          attributeId: testAttributeId,
          value: "Vàng",
          slug: "vang",
          sortOrder: 3,
        },
      ],
    });

    // Navigate to detail page
    await page.goto(`/admin/attributes/${testAttributeId}`);
  });

  test.afterAll(async () => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
    await prisma.$disconnect();
  });

  test("should display attribute details", async ({ page }) => {
    // Check page title
    await expect(page.locator("h1")).toContainText("Màu sắc");

    // Check basic information
    await expect(page.locator('[data-testid="attribute-name"]')).toContainText("Màu sắc");
    await expect(page.locator('[data-testid="attribute-slug"]')).toContainText("mau-sac");
    await expect(page.locator('[data-testid="attribute-type"]')).toContainText("Màu sắc");
    await expect(page.locator('[data-testid="attribute-description"]')).toContainText("Màu sắc sản phẩm");
    await expect(page.locator('[data-testid="sort-order"]')).toContainText("1");

    // Check status badges
    await expect(page.locator('[data-testid="filterable-badge"]')).toBeVisible();
    await expect(page.locator('[data-testid="required-badge"]')).not.toBeVisible();

    // Check creation date
    await expect(page.locator('[data-testid="created-date"]')).toBeVisible();
  });

  test("should display statistics", async ({ page }) => {
    // Check statistics cards
    await expect(page.locator('[data-testid="values-count"]')).toContainText("3");
    await expect(page.locator('[data-testid="products-count"]')).toContainText("0");
    await expect(page.locator('[data-testid="last-updated"]')).toBeVisible();
  });

  test("should display values list", async ({ page }) => {
    // Check values section
    await expect(page.locator('[data-testid="values-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="values-title"]')).toContainText("Danh sách giá trị (3)");

    // Check values table
    await expect(page.locator('[data-testid="values-table"]')).toBeVisible();
    await expect(page.locator('[data-testid="value-row"]')).toHaveCount(3);

    // Check value details
    const firstRow = page.locator('[data-testid="value-row"]').first();
    await expect(firstRow.locator('[data-testid="value-name"]')).toContainText("Đỏ");
    await expect(firstRow.locator('[data-testid="value-slug"]')).toContainText("do");
    await expect(firstRow.locator('[data-testid="value-sort-order"]')).toContainText("1");
    await expect(firstRow.locator('[data-testid="value-products-count"]')).toContainText("0");

    // Check color display for color attributes
    await expect(firstRow.locator('[data-testid="color-preview"]')).toBeVisible();
  });

  test("should navigate to edit page", async ({ page }) => {
    // Click edit button
    await page.click('[data-testid="edit-btn"]');

    // Should navigate to edit page
    await expect(page).toHaveURL(`/admin/attributes/${testAttributeId}/edit`);
    await expect(page.locator("h1")).toContainText("Chỉnh sửa thuộc tính");
  });

  test("should navigate back to list", async ({ page }) => {
    // Click back button
    await page.click('[data-testid="back-btn"]');

    // Should navigate to attributes list
    await expect(page).toHaveURL("/admin/attributes");
    await expect(page.locator("h1")).toContainText("Quản lý thuộc tính");
  });

  test("should delete attribute", async ({ page }) => {
    // Click delete button
    await page.click('[data-testid="delete-btn"]');

    // Confirm deletion
    await expect(page.locator('[data-testid="delete-dialog"]')).toBeVisible();
    await expect(page.locator("text=Bạn có chắc chắn muốn xóa thuộc tính")).toBeVisible();
    await page.click('[data-testid="confirm-delete-btn"]');

    // Should show success message
    await expect(page.locator("text=Xóa thuộc tính thành công")).toBeVisible();

    // Should redirect to attributes list
    await expect(page).toHaveURL("/admin/attributes");

    // Verify deletion in database
    const attribute = await prisma.attribute.findUnique({
      where: { id: testAttributeId },
    });
    expect(attribute).toBeNull();
  });

  test("should show usage warning when attribute is used", async ({ page }) => {
    // Create a product that uses this attribute
    const product = await prisma.product.create({
      data: {
        name: "Test Product",
        slug: "test-product",
        description: "Test product description",
        price: 100000,
        status: "ACTIVE",
        categoryId: null,
      },
    });

    const attributeValue = await prisma.attributeValue.findFirst({
      where: { attributeId: testAttributeId },
    });

    await prisma.productAttribute.create({
      data: {
        productId: product.id,
        attributeId: testAttributeId,
        attributeValueId: attributeValue!.id,
      },
    });

    // Reload page
    await page.reload();

    // Should show usage warning
    await expect(page.locator('[data-testid="usage-warning"]')).toBeVisible();
    await expect(page.locator("text=Thuộc tính đang được sử dụng")).toBeVisible();
    await expect(page.locator("text=1 sản phẩm")).toBeVisible();

    // Statistics should be updated
    await expect(page.locator('[data-testid="products-count"]')).toContainText("1");

    // Delete dialog should show warning
    await page.click('[data-testid="delete-btn"]');
    await expect(page.locator("text=Cảnh báo: Thuộc tính này đang được sử dụng")).toBeVisible();
  });

  test("should display empty state when no values", async ({ page }) => {
    // Create attribute without values
    const textAttribute = await prisma.attribute.create({
      data: {
        name: "Mô tả",
        slug: "mo-ta",
        type: "TEXT",
        isRequired: false,
        isFilterable: false,
        sortOrder: 2,
      },
    });

    // Navigate to text attribute detail
    await page.goto(`/admin/attributes/${textAttribute.id}`);

    // Should show empty state
    await expect(page.locator('[data-testid="no-values-state"]')).toBeVisible();
    await expect(page.locator("text=Chưa có giá trị nào")).toBeVisible();
    await expect(page.locator('[data-testid="add-values-btn"]')).toBeVisible();

    // Click add values button should navigate to edit
    await page.click('[data-testid="add-values-btn"]');
    await expect(page).toHaveURL(`/admin/attributes/${textAttribute.id}/edit`);
  });

  test("should handle different attribute types display", async ({ page }) => {
    // Test SIZE attribute
    const sizeAttribute = await prisma.attribute.create({
      data: {
        name: "Kích thước",
        slug: "kich-thuoc",
        type: "SIZE",
        isRequired: true,
        isFilterable: true,
        sortOrder: 2,
      },
    });

    await prisma.attributeValue.createMany({
      data: [
        { attributeId: sizeAttribute.id, value: "S", slug: "s", sortOrder: 1 },
        { attributeId: sizeAttribute.id, value: "M", slug: "m", sortOrder: 2 },
        { attributeId: sizeAttribute.id, value: "L", slug: "l", sortOrder: 3 },
      ],
    });

    // Navigate to size attribute
    await page.goto(`/admin/attributes/${sizeAttribute.id}`);

    // Should show size-specific display
    await expect(page.locator('[data-testid="attribute-type"]')).toContainText("Kích thước");
    await expect(page.locator('[data-testid="required-badge"]')).toBeVisible();

    // Values should show size badges
    await expect(page.locator('[data-testid="size-badge"]')).toHaveCount(3);
  });

  test("should handle boolean attribute display", async ({ page }) => {
    // Create boolean attribute
    const booleanAttribute = await prisma.attribute.create({
      data: {
        name: "Chống nước",
        slug: "chong-nuoc",
        type: "BOOLEAN",
        isRequired: false,
        isFilterable: true,
        sortOrder: 3,
      },
    });

    // Navigate to boolean attribute
    await page.goto(`/admin/attributes/${booleanAttribute.id}`);

    // Should show boolean-specific display
    await expect(page.locator('[data-testid="attribute-type"]')).toContainText("Có/Không");

    // Should not show values section for boolean
    await expect(page.locator('[data-testid="values-section"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="boolean-info"]')).toBeVisible();
    await expect(page.locator("text=Thuộc tính boolean không cần giá trị")).toBeVisible();
  });

  test("should sort values by sort order", async ({ page }) => {
    // Values should be displayed in sort order
    const valueRows = page.locator('[data-testid="value-row"]');
    
    await expect(valueRows.nth(0).locator('[data-testid="value-name"]')).toContainText("Đỏ");
    await expect(valueRows.nth(1).locator('[data-testid="value-name"]')).toContainText("Xanh dương");
    await expect(valueRows.nth(2).locator('[data-testid="value-name"]')).toContainText("Vàng");

    // Sort orders should be displayed
    await expect(valueRows.nth(0).locator('[data-testid="value-sort-order"]')).toContainText("1");
    await expect(valueRows.nth(1).locator('[data-testid="value-sort-order"]')).toContainText("2");
    await expect(valueRows.nth(2).locator('[data-testid="value-sort-order"]')).toContainText("3");
  });

  test("should handle loading state", async ({ page }) => {
    // Intercept API call to add delay
    await page.route(`/api/admin/attributes/${testAttributeId}`, async (route) => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });

    // Navigate to page
    await page.goto(`/admin/attributes/${testAttributeId}`);

    // Should show loading state
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
    await expect(page.locator("text=Đang tải thông tin thuộc tính")).toBeVisible();

    // Wait for loading to complete
    await expect(page.locator('[data-testid="loading-spinner"]')).not.toBeVisible();
    await expect(page.locator("h1")).toContainText("Màu sắc");
  });

  test("should handle 404 error", async ({ page }) => {
    // Navigate to non-existent attribute
    await page.goto("/admin/attributes/non-existent-id");

    // Should show 404 error
    await expect(page.locator('[data-testid="not-found-error"]')).toBeVisible();
    await expect(page.locator("text=Không tìm thấy thuộc tính")).toBeVisible();
    await expect(page.locator('[data-testid="back-to-list-btn"]')).toBeVisible();

    // Click back to list
    await page.click('[data-testid="back-to-list-btn"]');
    await expect(page).toHaveURL("/admin/attributes");
  });

  test("should handle server errors", async ({ page }) => {
    // Intercept API call to return error
    await page.route(`/api/admin/attributes/${testAttributeId}`, async (route) => {
      await route.fulfill({
        status: 500,
        contentType: "application/json",
        body: JSON.stringify({ error: "Internal server error" }),
      });
    });

    // Navigate to page
    await page.goto(`/admin/attributes/${testAttributeId}`);

    // Should show error state
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator("text=Có lỗi xảy ra")).toBeVisible();
    await expect(page.locator('[data-testid="retry-btn"]')).toBeVisible();

    // Click retry should reload
    await page.click('[data-testid="retry-btn"]');
    // Should attempt to reload (would show loading state again)
  });

  test("should display breadcrumb navigation", async ({ page }) => {
    // Should show breadcrumb
    await expect(page.locator('[data-testid="breadcrumb"]')).toBeVisible();
    await expect(page.locator('[data-testid="breadcrumb"]')).toContainText("Quản lý thuộc tính");
    await expect(page.locator('[data-testid="breadcrumb"]')).toContainText("Màu sắc");

    // Click breadcrumb link
    await page.click('[data-testid="breadcrumb-attributes"]');
    await expect(page).toHaveURL("/admin/attributes");
  });
});
