import { test, expect } from "@playwright/test";
import { prisma } from "@/lib/prisma";

test.describe("Attributes List Page", () => {
  test.beforeEach(async ({ page }) => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();

    // Create test attributes
    await prisma.attribute.createMany({
      data: [
        {
          name: "<PERSON><PERSON><PERSON> sắc",
          slug: "mau-sac",
          type: "COLOR",
          description: "<PERSON><PERSON><PERSON> sắc sản phẩm",
          isRequired: false,
          isFilterable: true,
          sortOrder: 1,
        },
        {
          name: "<PERSON><PERSON><PERSON> thước",
          slug: "kich-thuoc",
          type: "SIZE",
          description: "<PERSON><PERSON><PERSON> thước sản phẩm",
          isRequired: true,
          isFilterable: true,
          sortOrder: 2,
        },
        {
          name: "<PERSON><PERSON><PERSON> liệu",
          slug: "chat-lieu",
          type: "SELECT",
          description: "Chất liệu sản phẩm",
          isRequired: false,
          isFilterable: true,
          sortOrder: 3,
        },
      ],
    });

    // Navigate to attributes page
    await page.goto("/admin/attributes");
  });

  test.afterAll(async () => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
    await prisma.$disconnect();
  });

  test("should display attributes list", async ({ page }) => {
    // Check page title
    await expect(page.locator("h1")).toContainText("Quản lý thuộc tính");

    // Check that attributes are displayed
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(3);

    // Check attribute names
    await expect(page.locator("text=Màu sắc")).toBeVisible();
    await expect(page.locator("text=Kích thước")).toBeVisible();
    await expect(page.locator("text=Chất liệu")).toBeVisible();

    // Check attribute types
    await expect(page.locator("text=Màu sắc")).toBeVisible();
    await expect(page.locator("text=Kích thước")).toBeVisible();
    await expect(page.locator("text=Lựa chọn")).toBeVisible();

    // Check required badges
    await expect(page.locator('[data-testid="required-badge"]')).toHaveCount(1);

    // Check filterable badges
    await expect(page.locator('[data-testid="filterable-badge"]')).toHaveCount(3);
  });

  test("should search attributes", async ({ page }) => {
    // Search for "màu"
    await page.fill('[data-testid="search-input"]', "màu");
    await page.press('[data-testid="search-input"]', "Enter");

    // Should show only color attribute
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(1);
    await expect(page.locator("text=Màu sắc")).toBeVisible();
    await expect(page.locator("text=Kích thước")).not.toBeVisible();

    // Clear search
    await page.fill('[data-testid="search-input"]', "");
    await page.press('[data-testid="search-input"]', "Enter");

    // Should show all attributes again
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(3);
  });

  test("should filter by type", async ({ page }) => {
    // Open type filter
    await page.click('[data-testid="type-filter"]');
    await page.click('text=Màu sắc');

    // Should show only color attribute
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(1);
    await expect(page.locator("text=Màu sắc")).toBeVisible();

    // Reset filter
    await page.click('[data-testid="type-filter"]');
    await page.click('text=Tất cả');

    // Should show all attributes again
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(3);
  });

  test("should filter by required status", async ({ page }) => {
    // Filter by required
    await page.check('[data-testid="required-filter"]');

    // Should show only required attribute
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(1);
    await expect(page.locator("text=Kích thước")).toBeVisible();

    // Uncheck filter
    await page.uncheck('[data-testid="required-filter"]');

    // Should show all attributes again
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(3);
  });

  test("should sort attributes", async ({ page }) => {
    // Sort by name descending
    await page.click('[data-testid="sort-name"]');
    await page.click('[data-testid="sort-name"]'); // Click twice for descending

    // Check order
    const attributeNames = await page.locator('[data-testid="attribute-name"]').allTextContents();
    expect(attributeNames[0]).toContain("Màu sắc");
    expect(attributeNames[1]).toContain("Kích thước");
    expect(attributeNames[2]).toContain("Chất liệu");
  });

  test("should navigate to create page", async ({ page }) => {
    // Click create button
    await page.click('[data-testid="create-attribute-btn"]');

    // Should navigate to create page
    await expect(page).toHaveURL("/admin/attributes/create");
    await expect(page.locator("h1")).toContainText("Tạo thuộc tính mới");
  });

  test("should navigate to attribute detail", async ({ page }) => {
    // Click on first attribute
    await page.click('[data-testid="attribute-row"]:first-child [data-testid="view-btn"]');

    // Should navigate to detail page
    await expect(page).toHaveURL(/\/admin\/attributes\/[^\/]+$/);
    await expect(page.locator("h1")).toContainText("Màu sắc");
  });

  test("should navigate to edit page", async ({ page }) => {
    // Click edit button on first attribute
    await page.click('[data-testid="attribute-row"]:first-child [data-testid="edit-btn"]');

    // Should navigate to edit page
    await expect(page).toHaveURL(/\/admin\/attributes\/[^\/]+\/edit$/);
    await expect(page.locator("h1")).toContainText("Chỉnh sửa thuộc tính");
  });

  test("should delete attribute", async ({ page }) => {
    // Click delete button on first attribute
    await page.click('[data-testid="attribute-row"]:first-child [data-testid="delete-btn"]');

    // Confirm deletion in dialog
    await expect(page.locator('[data-testid="delete-dialog"]')).toBeVisible();
    await expect(page.locator("text=Bạn có chắc chắn muốn xóa")).toBeVisible();
    await page.click('[data-testid="confirm-delete-btn"]');

    // Should show success message
    await expect(page.locator("text=Xóa thuộc tính thành công")).toBeVisible();

    // Should have one less attribute
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(2);
    await expect(page.locator("text=Màu sắc")).not.toBeVisible();
  });

  test("should handle bulk operations", async ({ page }) => {
    // Select multiple attributes
    await page.check('[data-testid="attribute-row"]:first-child [data-testid="select-checkbox"]');
    await page.check('[data-testid="attribute-row"]:nth-child(2) [data-testid="select-checkbox"]');

    // Bulk actions should be visible
    await expect(page.locator('[data-testid="bulk-actions"]')).toBeVisible();
    await expect(page.locator('[data-testid="selected-count"]')).toContainText("2 đã chọn");

    // Test bulk delete
    await page.click('[data-testid="bulk-delete-btn"]');
    await expect(page.locator('[data-testid="bulk-delete-dialog"]')).toBeVisible();
    await page.click('[data-testid="confirm-bulk-delete-btn"]');

    // Should show success message
    await expect(page.locator("text=Đã xóa 2 thuộc tính thành công")).toBeVisible();

    // Should have one attribute left
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(1);
  });

  test("should handle pagination", async ({ page }) => {
    // Create more attributes to test pagination
    const moreAttributes = Array.from({ length: 25 }, (_, i) => ({
      name: `Thuộc tính ${i + 4}`,
      slug: `thuoc-tinh-${i + 4}`,
      type: "TEXT" as const,
      isRequired: false,
      isFilterable: true,
      sortOrder: i + 4,
    }));

    await prisma.attribute.createMany({
      data: moreAttributes,
    });

    // Reload page
    await page.reload();

    // Should show pagination
    await expect(page.locator('[data-testid="pagination"]')).toBeVisible();
    await expect(page.locator('[data-testid="page-info"]')).toContainText("1 / 2");

    // Go to next page
    await page.click('[data-testid="next-page-btn"]');
    await expect(page.locator('[data-testid="page-info"]')).toContainText("2 / 2");

    // Go back to first page
    await page.click('[data-testid="prev-page-btn"]');
    await expect(page.locator('[data-testid="page-info"]')).toContainText("1 / 2");
  });

  test("should show empty state when no attributes", async ({ page }) => {
    // Delete all attributes
    await prisma.attribute.deleteMany();

    // Reload page
    await page.reload();

    // Should show empty state
    await expect(page.locator('[data-testid="empty-state"]')).toBeVisible();
    await expect(page.locator("text=Chưa có thuộc tính nào")).toBeVisible();
    await expect(page.locator('[data-testid="create-first-attribute-btn"]')).toBeVisible();

    // Click create button in empty state
    await page.click('[data-testid="create-first-attribute-btn"]');
    await expect(page).toHaveURL("/admin/attributes/create");
  });

  test("should display attribute statistics", async ({ page }) => {
    // Should show statistics cards
    await expect(page.locator('[data-testid="total-attributes"]')).toContainText("3");
    await expect(page.locator('[data-testid="required-attributes"]')).toContainText("1");
    await expect(page.locator('[data-testid="filterable-attributes"]')).toContainText("3");

    // Should show type distribution
    await expect(page.locator('[data-testid="color-count"]')).toContainText("1");
    await expect(page.locator('[data-testid="size-count"]')).toContainText("1");
    await expect(page.locator('[data-testid="select-count"]')).toContainText("1");
  });

  test("should handle loading states", async ({ page }) => {
    // Intercept API call to add delay
    await page.route("/api/admin/attributes*", async (route) => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.continue();
    });

    // Navigate to page
    await page.goto("/admin/attributes");

    // Should show loading state
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();

    // Wait for loading to complete
    await expect(page.locator('[data-testid="loading-spinner"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(3);
  });

  test("should handle error states", async ({ page }) => {
    // Intercept API call to return error
    await page.route("/api/admin/attributes*", async (route) => {
      await route.fulfill({
        status: 500,
        contentType: "application/json",
        body: JSON.stringify({ error: "Internal server error" }),
      });
    });

    // Navigate to page
    await page.goto("/admin/attributes");

    // Should show error state
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator("text=Có lỗi xảy ra")).toBeVisible();

    // Should have retry button
    await expect(page.locator('[data-testid="retry-btn"]')).toBeVisible();
  });
});
