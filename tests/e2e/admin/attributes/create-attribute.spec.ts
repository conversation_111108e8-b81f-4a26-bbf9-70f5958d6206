import { test, expect } from "@playwright/test";
import { prisma } from "@/lib/prisma";

test.describe("Create Attribute Page", () => {
  test.beforeEach(async ({ page }) => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();

    // Navigate to create attribute page
    await page.goto("/admin/attributes/create");
  });

  test.afterAll(async () => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
    await prisma.$disconnect();
  });

  test("should display create form", async ({ page }) => {
    // Check page title
    await expect(page.locator("h1")).toContainText("Tạo thuộc tính mới");

    // Check form fields
    await expect(page.locator('[data-testid="name-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="slug-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="description-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="type-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="required-switch"]')).toBeVisible();
    await expect(page.locator('[data-testid="filterable-switch"]')).toBeVisible();
    await expect(page.locator('[data-testid="sort-order-input"]')).toBeVisible();

    // Check buttons
    await expect(page.locator('[data-testid="cancel-btn"]')).toBeVisible();
    await expect(page.locator('[data-testid="submit-btn"]')).toBeVisible();
  });

  test("should auto-generate slug from name", async ({ page }) => {
    // Type attribute name
    await page.fill('[data-testid="name-input"]', "Màu sắc đặc biệt");

    // Slug should be auto-generated
    await expect(page.locator('[data-testid="slug-input"]')).toHaveValue("mau-sac-dac-biet");
  });

  test("should create text attribute", async ({ page }) => {
    // Fill basic information
    await page.fill('[data-testid="name-input"]', "Mô tả sản phẩm");
    await page.fill('[data-testid="description-input"]', "Mô tả chi tiết về sản phẩm");

    // Select TEXT type
    await page.click('[data-testid="type-select"]');
    await page.click('text=Văn bản');

    // Set as not required and not filterable
    await page.uncheck('[data-testid="required-switch"]');
    await page.uncheck('[data-testid="filterable-switch"]');

    // Set sort order
    await page.fill('[data-testid="sort-order-input"]', "5");

    // Submit form
    await page.click('[data-testid="submit-btn"]');

    // Should show success message
    await expect(page.locator("text=Tạo thuộc tính thành công")).toBeVisible();

    // Should redirect to attributes list
    await expect(page).toHaveURL("/admin/attributes");

    // Should see new attribute in list
    await expect(page.locator("text=Mô tả sản phẩm")).toBeVisible();
  });

  test("should create color attribute with values", async ({ page }) => {
    // Fill basic information
    await page.fill('[data-testid="name-input"]', "Màu sắc");
    await page.fill('[data-testid="description-input"]', "Màu sắc sản phẩm");

    // Select COLOR type
    await page.click('[data-testid="type-select"]');
    await page.click('text=Màu sắc');

    // Values section should appear
    await expect(page.locator('[data-testid="values-section"]')).toBeVisible();

    // Add first value
    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="value-input-0"]', "Đỏ");
    // Slug should be auto-generated
    await expect(page.locator('[data-testid="value-slug-0"]')).toHaveValue("do");

    // Add second value
    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="value-input-1"]', "Xanh dương");
    await expect(page.locator('[data-testid="value-slug-1"]')).toHaveValue("xanh-duong");

    // Add third value
    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="value-input-2"]', "Vàng");

    // Remove second value
    await page.click('[data-testid="remove-value-btn-1"]');

    // Should have 2 values left
    await expect(page.locator('[data-testid="value-row"]')).toHaveCount(2);

    // Set as required and filterable
    await page.check('[data-testid="required-switch"]');
    await page.check('[data-testid="filterable-switch"]');

    // Submit form
    await page.click('[data-testid="submit-btn"]');

    // Should show success message
    await expect(page.locator("text=Tạo thuộc tính thành công")).toBeVisible();

    // Should redirect to attributes list
    await expect(page).toHaveURL("/admin/attributes");

    // Should see new attribute in list
    await expect(page.locator("text=Màu sắc")).toBeVisible();
    await expect(page.locator('[data-testid="required-badge"]')).toBeVisible();
  });

  test("should create size attribute with predefined values", async ({ page }) => {
    // Fill basic information
    await page.fill('[data-testid="name-input"]', "Kích thước");

    // Select SIZE type
    await page.click('[data-testid="type-select"]');
    await page.click('text=Kích thước');

    // Add size values
    const sizes = ["XS", "S", "M", "L", "XL"];
    for (let i = 0; i < sizes.length; i++) {
      await page.click('[data-testid="add-value-btn"]');
      await page.fill(`[data-testid="value-input-${i}"]`, sizes[i]);
    }

    // Submit form
    await page.click('[data-testid="submit-btn"]');

    // Should show success message
    await expect(page.locator("text=Tạo thuộc tính thành công")).toBeVisible();

    // Verify attribute was created with values
    const attribute = await prisma.attribute.findFirst({
      where: { name: "Kích thước" },
      include: { values: true },
    });

    expect(attribute).toBeTruthy();
    expect(attribute?.values).toHaveLength(5);
    expect(attribute?.values.map(v => v.value)).toEqual(sizes);
  });

  test("should validate required fields", async ({ page }) => {
    // Try to submit without name
    await page.click('[data-testid="submit-btn"]');

    // Should show validation error
    await expect(page.locator("text=Tên thuộc tính là bắt buộc")).toBeVisible();

    // Fill name but leave slug empty
    await page.fill('[data-testid="name-input"]', "Test Attribute");
    await page.fill('[data-testid="slug-input"]', "");
    await page.click('[data-testid="submit-btn"]');

    // Should show slug validation error
    await expect(page.locator("text=Slug là bắt buộc")).toBeVisible();
  });

  test("should validate values for types that need them", async ({ page }) => {
    // Fill basic information
    await page.fill('[data-testid="name-input"]', "Màu sắc");

    // Select COLOR type
    await page.click('[data-testid="type-select"]');
    await page.click('text=Màu sắc');

    // Try to submit without values
    await page.click('[data-testid="submit-btn"]');

    // Should show validation error
    await expect(page.locator("text=Loại thuộc tính này cần ít nhất một giá trị")).toBeVisible();

    // Add empty value
    await page.click('[data-testid="add-value-btn"]');
    await page.click('[data-testid="submit-btn"]');

    // Should show value validation error
    await expect(page.locator("text=Giá trị không được để trống")).toBeVisible();
  });

  test("should prevent duplicate values", async ({ page }) => {
    // Fill basic information
    await page.fill('[data-testid="name-input"]', "Màu sắc");

    // Select COLOR type
    await page.click('[data-testid="type-select"]');
    await page.click('text=Màu sắc');

    // Add duplicate values
    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="value-input-0"]', "Đỏ");

    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="value-input-1"]', "Đỏ");

    // Try to submit
    await page.click('[data-testid="submit-btn"]');

    // Should show validation error
    await expect(page.locator("text=Không được có giá trị trùng lặp")).toBeVisible();
  });

  test("should handle drag and drop reordering", async ({ page }) => {
    // Fill basic information
    await page.fill('[data-testid="name-input"]', "Màu sắc");

    // Select COLOR type
    await page.click('[data-testid="type-select"]');
    await page.click('text=Màu sắc');

    // Add values
    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="value-input-0"]', "Đỏ");

    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="value-input-1"]', "Xanh");

    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="value-input-2"]', "Vàng");

    // Drag second item to first position
    const secondItem = page.locator('[data-testid="value-row"]:nth-child(2)');
    const firstItem = page.locator('[data-testid="value-row"]:nth-child(1)');

    await secondItem.dragTo(firstItem);

    // Check that order changed
    await expect(page.locator('[data-testid="value-input-0"]')).toHaveValue("Xanh");
    await expect(page.locator('[data-testid="value-input-1"]')).toHaveValue("Đỏ");
    await expect(page.locator('[data-testid="value-input-2"]')).toHaveValue("Vàng");
  });

  test("should cancel and return to list", async ({ page }) => {
    // Fill some data
    await page.fill('[data-testid="name-input"]', "Test Attribute");

    // Click cancel
    await page.click('[data-testid="cancel-btn"]');

    // Should redirect to attributes list
    await expect(page).toHaveURL("/admin/attributes");

    // Should not have created the attribute
    const attribute = await prisma.attribute.findFirst({
      where: { name: "Test Attribute" },
    });
    expect(attribute).toBeNull();
  });

  test("should show type descriptions", async ({ page }) => {
    // Click on type select
    await page.click('[data-testid="type-select"]');

    // Should show type options with descriptions
    await expect(page.locator("text=Văn bản")).toBeVisible();
    await expect(page.locator("text=Cho phép nhập văn bản tự do")).toBeVisible();

    await expect(page.locator("text=Màu sắc")).toBeVisible();
    await expect(page.locator("text=Danh sách màu sắc có thể chọn")).toBeVisible();

    await expect(page.locator("text=Kích thước")).toBeVisible();
    await expect(page.locator("text=Danh sách kích thước có thể chọn")).toBeVisible();
  });

  test("should handle loading state during submission", async ({ page }) => {
    // Fill form
    await page.fill('[data-testid="name-input"]', "Test Attribute");

    // Intercept API call to add delay
    await page.route("/api/admin/attributes", async (route) => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });

    // Submit form
    await page.click('[data-testid="submit-btn"]');

    // Should show loading state
    await expect(page.locator('[data-testid="submit-btn"]')).toContainText("Đang tạo...");
    await expect(page.locator('[data-testid="submit-btn"]')).toBeDisabled();

    // Wait for completion
    await expect(page.locator("text=Tạo thuộc tính thành công")).toBeVisible();
  });

  test("should handle server errors", async ({ page }) => {
    // Fill form
    await page.fill('[data-testid="name-input"]', "Test Attribute");

    // Intercept API call to return error
    await page.route("/api/admin/attributes", async (route) => {
      await route.fulfill({
        status: 500,
        contentType: "application/json",
        body: JSON.stringify({ error: "Internal server error" }),
      });
    });

    // Submit form
    await page.click('[data-testid="submit-btn"]');

    // Should show error message
    await expect(page.locator("text=Internal server error")).toBeVisible();

    // Form should remain filled
    await expect(page.locator('[data-testid="name-input"]')).toHaveValue("Test Attribute");
  });
});
