import { test, expect } from "@playwright/test";
import { prisma } from "@/lib/prisma";

test.describe("Edit Attribute Page", () => {
  let testAttributeId: string;

  test.beforeEach(async ({ page }) => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();

    // Create test attribute with values
    const testAttribute = await prisma.attribute.create({
      data: {
        name: "<PERSON><PERSON><PERSON> sắc",
        slug: "mau-sac",
        type: "COLOR",
        description: "<PERSON><PERSON><PERSON> sắc sản phẩm",
        isRequired: false,
        isFilterable: true,
        sortOrder: 1,
      },
    });

    testAttributeId = testAttribute.id;

    // Create test values
    await prisma.attributeValue.createMany({
      data: [
        {
          attributeId: testAttributeId,
          value: "Đỏ",
          slug: "do",
          sortOrder: 1,
        },
        {
          attributeId: testAttributeId,
          value: "Xanh",
          slug: "xanh",
          sortOrder: 2,
        },
        {
          attributeId: testAttributeId,
          value: "Vàng",
          slug: "vang",
          sortOrder: 3,
        },
      ],
    });

    // Navigate to edit page
    await page.goto(`/admin/attributes/${testAttributeId}/edit`);
  });

  test.afterAll(async () => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
    await prisma.$disconnect();
  });

  test("should display edit form with existing data", async ({ page }) => {
    // Check page title
    await expect(page.locator("h1")).toContainText("Chỉnh sửa thuộc tính");

    // Check form is pre-filled
    await expect(page.locator('[data-testid="name-input"]')).toHaveValue("Màu sắc");
    await expect(page.locator('[data-testid="slug-input"]')).toHaveValue("mau-sac");
    await expect(page.locator('[data-testid="description-input"]')).toHaveValue("Màu sắc sản phẩm");
    await expect(page.locator('[data-testid="type-select"]')).toContainText("Màu sắc");
    await expect(page.locator('[data-testid="required-switch"]')).not.toBeChecked();
    await expect(page.locator('[data-testid="filterable-switch"]')).toBeChecked();
    await expect(page.locator('[data-testid="sort-order-input"]')).toHaveValue("1");

    // Check values are displayed
    await expect(page.locator('[data-testid="values-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="value-row"]')).toHaveCount(3);
    await expect(page.locator("text=Đỏ")).toBeVisible();
    await expect(page.locator("text=Xanh")).toBeVisible();
    await expect(page.locator("text=Vàng")).toBeVisible();
  });

  test("should update basic attribute information", async ({ page }) => {
    // Update name
    await page.fill('[data-testid="name-input"]', "Màu sắc cập nhật");

    // Update description
    await page.fill('[data-testid="description-input"]', "Màu sắc sản phẩm đã cập nhật");

    // Toggle required
    await page.check('[data-testid="required-switch"]');

    // Update sort order
    await page.fill('[data-testid="sort-order-input"]', "5");

    // Submit form
    await page.click('[data-testid="submit-btn"]');

    // Should show success message
    await expect(page.locator("text=Cập nhật thuộc tính thành công")).toBeVisible();

    // Verify changes in database
    const updatedAttribute = await prisma.attribute.findUnique({
      where: { id: testAttributeId },
    });

    expect(updatedAttribute?.name).toBe("Màu sắc cập nhật");
    expect(updatedAttribute?.description).toBe("Màu sắc sản phẩm đã cập nhật");
    expect(updatedAttribute?.isRequired).toBe(true);
    expect(updatedAttribute?.sortOrder).toBe(5);
  });

  test("should add new attribute value", async ({ page }) => {
    // Click add value button
    await page.click('[data-testid="add-value-btn"]');

    // Fill new value
    await page.fill('[data-testid="new-value-input"]', "Tím");

    // Submit new value
    await page.click('[data-testid="save-value-btn"]');

    // Should show success message
    await expect(page.locator("text=Tạo giá trị thành công")).toBeVisible();

    // Should see new value in list
    await expect(page.locator("text=Tím")).toBeVisible();
    await expect(page.locator('[data-testid="value-row"]')).toHaveCount(4);

    // Verify in database
    const values = await prisma.attributeValue.findMany({
      where: { attributeId: testAttributeId },
    });
    expect(values).toHaveLength(4);
    expect(values.some(v => v.value === "Tím")).toBe(true);
  });

  test("should edit existing attribute value", async ({ page }) => {
    // Click edit button on first value
    await page.click('[data-testid="value-row"]:first-child [data-testid="edit-value-btn"]');

    // Update value
    await page.fill('[data-testid="edit-value-input"]', "Đỏ đậm");
    await page.fill('[data-testid="edit-slug-input"]', "do-dam");

    // Save changes
    await page.click('[data-testid="save-value-btn"]');

    // Should show success message
    await expect(page.locator("text=Cập nhật giá trị thành công")).toBeVisible();

    // Should see updated value
    await expect(page.locator("text=Đỏ đậm")).toBeVisible();
    await expect(page.locator("text=Đỏ")).not.toBeVisible();

    // Verify in database
    const value = await prisma.attributeValue.findFirst({
      where: { attributeId: testAttributeId, slug: "do-dam" },
    });
    expect(value?.value).toBe("Đỏ đậm");
  });

  test("should delete attribute value", async ({ page }) => {
    // Click delete button on first value
    await page.click('[data-testid="value-row"]:first-child [data-testid="delete-value-btn"]');

    // Confirm deletion
    await expect(page.locator('[data-testid="delete-value-dialog"]')).toBeVisible();
    await page.click('[data-testid="confirm-delete-value-btn"]');

    // Should show success message
    await expect(page.locator("text=Xóa giá trị thành công")).toBeVisible();

    // Should have one less value
    await expect(page.locator('[data-testid="value-row"]')).toHaveCount(2);
    await expect(page.locator("text=Đỏ")).not.toBeVisible();

    // Verify in database
    const values = await prisma.attributeValue.findMany({
      where: { attributeId: testAttributeId },
    });
    expect(values).toHaveLength(2);
    expect(values.some(v => v.value === "Đỏ")).toBe(false);
  });

  test("should reorder attribute values", async ({ page }) => {
    // Drag third value to first position
    const thirdValue = page.locator('[data-testid="value-row"]:nth-child(3)');
    const firstValue = page.locator('[data-testid="value-row"]:nth-child(1)');

    await thirdValue.dragTo(firstValue);

    // Should show success message
    await expect(page.locator("text=Cập nhật thứ tự thành công")).toBeVisible();

    // Check new order
    const valueRows = page.locator('[data-testid="value-row"]');
    await expect(valueRows.nth(0)).toContainText("Vàng");
    await expect(valueRows.nth(1)).toContainText("Đỏ");
    await expect(valueRows.nth(2)).toContainText("Xanh");

    // Verify in database
    const values = await prisma.attributeValue.findMany({
      where: { attributeId: testAttributeId },
      orderBy: { sortOrder: "asc" },
    });
    expect(values[0].value).toBe("Vàng");
    expect(values[1].value).toBe("Đỏ");
    expect(values[2].value).toBe("Xanh");
  });

  test("should prevent duplicate values", async ({ page }) => {
    // Try to add existing value
    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="new-value-input"]', "Đỏ");
    await page.click('[data-testid="save-value-btn"]');

    // Should show error message
    await expect(page.locator("text=Giá trị hoặc slug đã tồn tại")).toBeVisible();

    // Should not add duplicate value
    const redValues = await page.locator("text=Đỏ").count();
    expect(redValues).toBe(1);
  });

  test("should delete entire attribute", async ({ page }) => {
    // Click delete attribute button
    await page.click('[data-testid="delete-attribute-btn"]');

    // Confirm deletion
    await expect(page.locator('[data-testid="delete-attribute-dialog"]')).toBeVisible();
    await expect(page.locator("text=Bạn có chắc chắn muốn xóa thuộc tính")).toBeVisible();
    await page.click('[data-testid="confirm-delete-attribute-btn"]');

    // Should show success message
    await expect(page.locator("text=Xóa thuộc tính thành công")).toBeVisible();

    // Should redirect to attributes list
    await expect(page).toHaveURL("/admin/attributes");

    // Verify deletion in database
    const attribute = await prisma.attribute.findUnique({
      where: { id: testAttributeId },
    });
    expect(attribute).toBeNull();

    // Values should also be deleted
    const values = await prisma.attributeValue.findMany({
      where: { attributeId: testAttributeId },
    });
    expect(values).toHaveLength(0);
  });

  test("should navigate to detail view", async ({ page }) => {
    // Click view detail button
    await page.click('[data-testid="view-detail-btn"]');

    // Should navigate to detail page
    await expect(page).toHaveURL(`/admin/attributes/${testAttributeId}`);
    await expect(page.locator("h1")).toContainText("Màu sắc");
  });

  test("should cancel and return to list", async ({ page }) => {
    // Make some changes
    await page.fill('[data-testid="name-input"]', "Changed Name");

    // Click cancel
    await page.click('[data-testid="cancel-btn"]');

    // Should redirect to attributes list
    await expect(page).toHaveURL("/admin/attributes");

    // Changes should not be saved
    const attribute = await prisma.attribute.findUnique({
      where: { id: testAttributeId },
    });
    expect(attribute?.name).toBe("Màu sắc");
  });

  test("should show usage warning when attribute is used by products", async ({ page }) => {
    // Create a product that uses this attribute
    const product = await prisma.product.create({
      data: {
        name: "Test Product",
        slug: "test-product",
        description: "Test product description",
        price: 100000,
        status: "ACTIVE",
        categoryId: null,
      },
    });

    const attributeValue = await prisma.attributeValue.findFirst({
      where: { attributeId: testAttributeId },
    });

    await prisma.productAttribute.create({
      data: {
        productId: product.id,
        attributeId: testAttributeId,
        attributeValueId: attributeValue!.id,
      },
    });

    // Reload page
    await page.reload();

    // Should show usage warning
    await expect(page.locator('[data-testid="usage-warning"]')).toBeVisible();
    await expect(page.locator("text=đang được sử dụng bởi")).toBeVisible();
    await expect(page.locator("text=1 sản phẩm")).toBeVisible();

    // Delete button should show warning
    await page.click('[data-testid="delete-attribute-btn"]');
    await expect(page.locator("text=Cảnh báo: Thuộc tính này đang được sử dụng")).toBeVisible();
  });

  test("should handle loading states", async ({ page }) => {
    // Intercept API calls to add delay
    await page.route("/api/admin/attributes/*", async (route) => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.continue();
    });

    // Update attribute
    await page.fill('[data-testid="name-input"]', "Updated Name");
    await page.click('[data-testid="submit-btn"]');

    // Should show loading state
    await expect(page.locator('[data-testid="submit-btn"]')).toContainText("Đang lưu...");
    await expect(page.locator('[data-testid="submit-btn"]')).toBeDisabled();

    // Wait for completion
    await expect(page.locator("text=Cập nhật thuộc tính thành công")).toBeVisible();
  });

  test("should handle server errors", async ({ page }) => {
    // Intercept API call to return error
    await page.route(`/api/admin/attributes/${testAttributeId}`, async (route) => {
      if (route.request().method() === "PUT") {
        await route.fulfill({
          status: 500,
          contentType: "application/json",
          body: JSON.stringify({ error: "Internal server error" }),
        });
      } else {
        await route.continue();
      }
    });

    // Try to update
    await page.fill('[data-testid="name-input"]', "Updated Name");
    await page.click('[data-testid="submit-btn"]');

    // Should show error message
    await expect(page.locator("text=Internal server error")).toBeVisible();

    // Form should remain filled
    await expect(page.locator('[data-testid="name-input"]')).toHaveValue("Updated Name");
  });
});
