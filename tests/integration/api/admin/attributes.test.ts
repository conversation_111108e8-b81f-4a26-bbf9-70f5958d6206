import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { GET, POST } from "@/app/api/admin/attributes/route";
import { GET as getById, PUT, DELETE } from "@/app/api/admin/attributes/[id]/route";
import { getServerSession } from "next-auth";
import { AttributeType } from "@/types/attribute";

// Mock next-auth
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

jest.mock("@/lib/admin-auth", () => ({
  authOptions: {},
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe("Attributes API Integration Tests", () => {
  beforeAll(async () => {
    // Set up authenticated session
    mockGetServerSession.mockResolvedValue({
      user: { role: "ADMIN" },
    } as any);
  });

  beforeEach(async () => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
    await prisma.$disconnect();
  });

  describe("Attributes CRUD Operations", () => {
    it("should create, read, update, and delete an attribute", async () => {
      // CREATE
      const createData = {
        name: "Màu sắc",
        type: "COLOR" as AttributeType,
        isRequired: false,
        isFilterable: true,
        sortOrder: 1,
        values: [
          { value: "Đỏ", slug: "do", sortOrder: 1 },
          { value: "Xanh", slug: "xanh", sortOrder: 2 },
        ],
      };

      const createRequest = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(createData),
      });

      const createResponse = await POST(createRequest);
      expect(createResponse.status).toBe(201);

      const createdAttribute = await createResponse.json();
      expect(createdAttribute.name).toBe(createData.name);
      expect(createdAttribute.slug).toBe("mau-sac");
      expect(createdAttribute.type).toBe(createData.type);

      const attributeId = createdAttribute.id;

      // READ (Get by ID)
      const getRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${attributeId}`);
      const getResponse = await getById(getRequest, { params: { id: attributeId } });
      expect(getResponse.status).toBe(200);

      const fetchedAttribute = await getResponse.json();
      expect(fetchedAttribute.id).toBe(attributeId);
      expect(fetchedAttribute.values).toHaveLength(2);

      // READ (List all)
      const listRequest = new NextRequest("http://localhost:3000/api/admin/attributes");
      const listResponse = await GET(listRequest);
      expect(listResponse.status).toBe(200);

      const listData = await listResponse.json();
      expect(listData.data).toHaveLength(1);
      expect(listData.data[0].id).toBe(attributeId);

      // UPDATE
      const updateData = {
        name: "Màu sắc cập nhật",
        description: "Màu sắc sản phẩm",
        isRequired: true,
      };

      const updateRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${attributeId}`, {
        method: "PUT",
        body: JSON.stringify(updateData),
      });

      const updateResponse = await PUT(updateRequest, { params: { id: attributeId } });
      expect(updateResponse.status).toBe(200);

      const updatedAttribute = await updateResponse.json();
      expect(updatedAttribute.name).toBe(updateData.name);
      expect(updatedAttribute.description).toBe(updateData.description);
      expect(updatedAttribute.isRequired).toBe(updateData.isRequired);

      // DELETE
      const deleteRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${attributeId}`, {
        method: "DELETE",
      });

      const deleteResponse = await DELETE(deleteRequest, { params: { id: attributeId } });
      expect(deleteResponse.status).toBe(200);

      // Verify deletion
      const verifyRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${attributeId}`);
      const verifyResponse = await getById(verifyRequest, { params: { id: attributeId } });
      expect(verifyResponse.status).toBe(404);
    });

    it("should handle attribute with no values", async () => {
      const createData = {
        name: "Mô tả sản phẩm",
        type: "TEXT" as AttributeType,
        isRequired: false,
        isFilterable: false,
        sortOrder: 0,
      };

      const createRequest = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(createData),
      });

      const createResponse = await POST(createRequest);
      expect(createResponse.status).toBe(201);

      const createdAttribute = await createResponse.json();
      expect(createdAttribute.name).toBe(createData.name);
      expect(createdAttribute.type).toBe(createData.type);
      expect(createdAttribute.slug).toBe("mo-ta-san-pham");
    });

    it("should prevent duplicate slugs", async () => {
      // Create first attribute
      const firstData = {
        name: "First Attribute",
        slug: "test-slug",
        type: "TEXT" as AttributeType,
      };

      const firstRequest = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(firstData),
      });

      const firstResponse = await POST(firstRequest);
      expect(firstResponse.status).toBe(201);

      // Try to create second attribute with same slug
      const secondData = {
        name: "Second Attribute",
        slug: "test-slug",
        type: "TEXT" as AttributeType,
      };

      const secondRequest = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(secondData),
      });

      const secondResponse = await POST(secondRequest);
      expect(secondResponse.status).toBe(400);

      const errorData = await secondResponse.json();
      expect(errorData.error).toBe("Slug đã tồn tại");
    });
  });

  describe("Filtering and Pagination", () => {
    beforeEach(async () => {
      // Create test attributes
      await prisma.attribute.createMany({
        data: [
          {
            name: "Màu sắc",
            slug: "mau-sac",
            type: "COLOR",
            isRequired: false,
            isFilterable: true,
            sortOrder: 1,
          },
          {
            name: "Kích thước",
            slug: "kich-thuoc",
            type: "SIZE",
            isRequired: true,
            isFilterable: true,
            sortOrder: 2,
          },
          {
            name: "Mô tả",
            slug: "mo-ta",
            type: "TEXT",
            isRequired: false,
            isFilterable: false,
            sortOrder: 3,
          },
        ],
      });
    });

    it("should filter by search term", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/attributes?search=màu");
      const response = await GET(request);
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.data).toHaveLength(1);
      expect(data.data[0].name).toBe("Màu sắc");
    });

    it("should filter by type", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/attributes?type=COLOR");
      const response = await GET(request);
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.data).toHaveLength(1);
      expect(data.data[0].type).toBe("COLOR");
    });

    it("should filter by required status", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/attributes?isRequired=true");
      const response = await GET(request);
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.data).toHaveLength(1);
      expect(data.data[0].isRequired).toBe(true);
    });

    it("should filter by filterable status", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/attributes?isFilterable=false");
      const response = await GET(request);
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.data).toHaveLength(1);
      expect(data.data[0].isFilterable).toBe(false);
    });

    it("should handle pagination", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/attributes?page=1&limit=2");
      const response = await GET(request);
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.data).toHaveLength(2);
      expect(data.pagination.page).toBe(1);
      expect(data.pagination.limit).toBe(2);
      expect(data.pagination.total).toBe(3);
      expect(data.pagination.pages).toBe(2);
    });

    it("should sort by different fields", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/attributes?sortBy=name&sortOrder=desc");
      const response = await GET(request);
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.data[0].name).toBe("Mô tả");
      expect(data.data[1].name).toBe("Màu sắc");
      expect(data.data[2].name).toBe("Kích thước");
    });
  });

  describe("Error Handling", () => {
    it("should return 404 for non-existent attribute", async () => {
      const request = new NextRequest("http://localhost:3000/api/admin/attributes/non-existent-id");
      const response = await getById(request, { params: { id: "non-existent-id" } });
      expect(response.status).toBe(404);

      const data = await response.json();
      expect(data.error).toBe("Không tìm thấy thuộc tính");
    });

    it("should validate required fields", async () => {
      const invalidData = {
        // Missing name field
        type: "TEXT",
      };

      const request = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(invalidData),
      });

      const response = await POST(request);
      expect(response.status).toBe(400);

      const data = await response.json();
      expect(data.error).toBe("Dữ liệu không hợp lệ");
    });

    it("should validate attribute type", async () => {
      const invalidData = {
        name: "Test Attribute",
        type: "INVALID_TYPE",
      };

      const request = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(invalidData),
      });

      const response = await POST(request);
      expect(response.status).toBe(400);
    });
  });

  describe("Database Transactions", () => {
    it("should rollback transaction on error during attribute creation with values", async () => {
      const createData = {
        name: "Test Attribute",
        type: "SELECT" as AttributeType,
        values: [
          { value: "Value 1", slug: "value-1", sortOrder: 1 },
          { value: "Value 2", slug: "value-2", sortOrder: 2 },
        ],
      };

      // Mock a database error during value creation
      const originalCreateMany = prisma.attributeValue.createMany;
      prisma.attributeValue.createMany = jest.fn().mockRejectedValue(new Error("Database error"));

      const request = new NextRequest("http://localhost:3000/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(createData),
      });

      const response = await POST(request);
      expect(response.status).toBe(500);

      // Verify that no attribute was created
      const attributes = await prisma.attribute.findMany();
      expect(attributes).toHaveLength(0);

      // Restore original function
      prisma.attributeValue.createMany = originalCreateMany;
    });
  });
});
