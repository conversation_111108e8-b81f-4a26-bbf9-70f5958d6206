import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { GET, POST } from "@/app/api/admin/attributes/[id]/values/route";
import { GET as getById, PUT, DELETE } from "@/app/api/admin/attributes/[id]/values/[valueId]/route";
import { PUT as reorder } from "@/app/api/admin/attributes/[id]/values/reorder/route";
import { getServerSession } from "next-auth";
import { AttributeType } from "@/types/attribute";

// Mock next-auth
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

jest.mock("@/lib/admin-auth", () => ({
  authOptions: {},
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe("Attribute Values API Integration Tests", () => {
  let testAttributeId: string;

  beforeAll(async () => {
    // Set up authenticated session
    mockGetServerSession.mockResolvedValue({
      user: { role: "ADMIN" },
    } as any);
  });

  beforeEach(async () => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();

    // Create test attribute
    const testAttribute = await prisma.attribute.create({
      data: {
        name: "Màu sắc",
        slug: "mau-sac",
        type: "COLOR" as AttributeType,
        isRequired: false,
        isFilterable: true,
        sortOrder: 1,
      },
    });

    testAttributeId = testAttribute.id;
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
    await prisma.$disconnect();
  });

  describe("Attribute Values CRUD Operations", () => {
    it("should create, read, update, and delete an attribute value", async () => {
      // CREATE
      const createData = {
        value: "Đỏ",
        slug: "do",
        sortOrder: 1,
      };

      const createRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values`, {
        method: "POST",
        body: JSON.stringify(createData),
      });

      const createResponse = await POST(createRequest, { params: { id: testAttributeId } });
      expect(createResponse.status).toBe(201);

      const createdValue = await createResponse.json();
      expect(createdValue.value).toBe(createData.value);
      expect(createdValue.slug).toBe(createData.slug);
      expect(createdValue.attributeId).toBe(testAttributeId);

      const valueId = createdValue.id;

      // READ (Get by ID)
      const getRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values/${valueId}`);
      const getResponse = await getById(getRequest, { params: { id: testAttributeId, valueId } });
      expect(getResponse.status).toBe(200);

      const fetchedValue = await getResponse.json();
      expect(fetchedValue.id).toBe(valueId);
      expect(fetchedValue.value).toBe(createData.value);

      // READ (List all values for attribute)
      const listRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values`);
      const listResponse = await GET(listRequest, { params: { id: testAttributeId } });
      expect(listResponse.status).toBe(200);

      const listData = await listResponse.json();
      expect(listData.data).toHaveLength(1);
      expect(listData.data[0].id).toBe(valueId);

      // UPDATE
      const updateData = {
        value: "Đỏ đậm",
        slug: "do-dam",
        sortOrder: 2,
      };

      const updateRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values/${valueId}`, {
        method: "PUT",
        body: JSON.stringify(updateData),
      });

      const updateResponse = await PUT(updateRequest, { params: { id: testAttributeId, valueId } });
      expect(updateResponse.status).toBe(200);

      const updatedValue = await updateResponse.json();
      expect(updatedValue.value).toBe(updateData.value);
      expect(updatedValue.slug).toBe(updateData.slug);
      expect(updatedValue.sortOrder).toBe(updateData.sortOrder);

      // DELETE
      const deleteRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values/${valueId}`, {
        method: "DELETE",
      });

      const deleteResponse = await DELETE(deleteRequest, { params: { id: testAttributeId, valueId } });
      expect(deleteResponse.status).toBe(200);

      // Verify deletion
      const verifyRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values/${valueId}`);
      const verifyResponse = await getById(verifyRequest, { params: { id: testAttributeId, valueId } });
      expect(verifyResponse.status).toBe(404);
    });

    it("should auto-generate slug when not provided", async () => {
      const createData = {
        value: "Xanh dương đậm",
        sortOrder: 1,
      };

      const createRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values`, {
        method: "POST",
        body: JSON.stringify(createData),
      });

      const createResponse = await POST(createRequest, { params: { id: testAttributeId } });
      expect(createResponse.status).toBe(201);

      const createdValue = await createResponse.json();
      expect(createdValue.value).toBe(createData.value);
      expect(createdValue.slug).toBe("xanh-duong-dam");
    });

    it("should prevent duplicate values within same attribute", async () => {
      // Create first value
      const firstData = {
        value: "Đỏ",
        slug: "do",
        sortOrder: 1,
      };

      const firstRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values`, {
        method: "POST",
        body: JSON.stringify(firstData),
      });

      const firstResponse = await POST(firstRequest, { params: { id: testAttributeId } });
      expect(firstResponse.status).toBe(201);

      // Try to create second value with same name
      const secondData = {
        value: "Đỏ",
        slug: "do-2",
        sortOrder: 2,
      };

      const secondRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values`, {
        method: "POST",
        body: JSON.stringify(secondData),
      });

      const secondResponse = await POST(secondRequest, { params: { id: testAttributeId } });
      expect(secondResponse.status).toBe(400);

      const errorData = await secondResponse.json();
      expect(errorData.error).toBe("Giá trị hoặc slug đã tồn tại");
    });

    it("should prevent duplicate slugs within same attribute", async () => {
      // Create first value
      const firstData = {
        value: "Đỏ",
        slug: "do",
        sortOrder: 1,
      };

      const firstRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values`, {
        method: "POST",
        body: JSON.stringify(firstData),
      });

      const firstResponse = await POST(firstRequest, { params: { id: testAttributeId } });
      expect(firstResponse.status).toBe(201);

      // Try to create second value with same slug
      const secondData = {
        value: "Đỏ tươi",
        slug: "do",
        sortOrder: 2,
      };

      const secondRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values`, {
        method: "POST",
        body: JSON.stringify(secondData),
      });

      const secondResponse = await POST(secondRequest, { params: { id: testAttributeId } });
      expect(secondResponse.status).toBe(400);

      const errorData = await secondResponse.json();
      expect(errorData.error).toBe("Giá trị hoặc slug đã tồn tại");
    });
  });

  describe("Value Reordering", () => {
    let valueIds: string[];

    beforeEach(async () => {
      // Create test values
      const values = await Promise.all([
        prisma.attributeValue.create({
          data: {
            attributeId: testAttributeId,
            value: "Đỏ",
            slug: "do",
            sortOrder: 1,
          },
        }),
        prisma.attributeValue.create({
          data: {
            attributeId: testAttributeId,
            value: "Xanh",
            slug: "xanh",
            sortOrder: 2,
          },
        }),
        prisma.attributeValue.create({
          data: {
            attributeId: testAttributeId,
            value: "Vàng",
            slug: "vang",
            sortOrder: 3,
          },
        }),
      ]);

      valueIds = values.map(v => v.id);
    });

    it("should reorder values correctly", async () => {
      const reorderData = {
        values: [
          { id: valueIds[2], sortOrder: 1 }, // Vàng -> 1
          { id: valueIds[0], sortOrder: 2 }, // Đỏ -> 2
          { id: valueIds[1], sortOrder: 3 }, // Xanh -> 3
        ],
      };

      const reorderRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values/reorder`, {
        method: "PUT",
        body: JSON.stringify(reorderData),
      });

      const reorderResponse = await reorder(reorderRequest, { params: { id: testAttributeId } });
      expect(reorderResponse.status).toBe(200);

      const responseData = await reorderResponse.json();
      expect(responseData.data).toHaveLength(3);

      // Verify new order
      const orderedValues = responseData.data.sort((a: any, b: any) => a.sortOrder - b.sortOrder);
      expect(orderedValues[0].value).toBe("Vàng");
      expect(orderedValues[1].value).toBe("Đỏ");
      expect(orderedValues[2].value).toBe("Xanh");
    });

    it("should reject reordering with invalid value IDs", async () => {
      const reorderData = {
        values: [
          { id: "invalid-id", sortOrder: 1 },
          { id: valueIds[0], sortOrder: 2 },
        ],
      };

      const reorderRequest = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values/reorder`, {
        method: "PUT",
        body: JSON.stringify(reorderData),
      });

      const reorderResponse = await reorder(reorderRequest, { params: { id: testAttributeId } });
      expect(reorderResponse.status).toBe(400);

      const errorData = await reorderResponse.json();
      expect(errorData.error).toBe("Một số giá trị không thuộc về thuộc tính này");
    });
  });

  describe("Error Handling", () => {
    it("should return 404 for non-existent attribute", async () => {
      const createData = {
        value: "Test Value",
        sortOrder: 1,
      };

      const request = new NextRequest("http://localhost:3000/api/admin/attributes/non-existent-id/values", {
        method: "POST",
        body: JSON.stringify(createData),
      });

      const response = await POST(request, { params: { id: "non-existent-id" } });
      expect(response.status).toBe(404);

      const data = await response.json();
      expect(data.error).toBe("Không tìm thấy thuộc tính");
    });

    it("should return 404 for non-existent value", async () => {
      const request = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values/non-existent-id`);
      const response = await getById(request, { params: { id: testAttributeId, valueId: "non-existent-id" } });
      expect(response.status).toBe(404);

      const data = await response.json();
      expect(data.error).toBe("Không tìm thấy giá trị thuộc tính");
    });

    it("should validate required fields", async () => {
      const invalidData = {
        // Missing value field
        sortOrder: 1,
      };

      const request = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values`, {
        method: "POST",
        body: JSON.stringify(invalidData),
      });

      const response = await POST(request, { params: { id: testAttributeId } });
      expect(response.status).toBe(400);

      const data = await response.json();
      expect(data.error).toBe("Dữ liệu không hợp lệ");
    });
  });

  describe("Pagination and Filtering", () => {
    beforeEach(async () => {
      // Create multiple test values
      await prisma.attributeValue.createMany({
        data: [
          { attributeId: testAttributeId, value: "Đỏ", slug: "do", sortOrder: 1 },
          { attributeId: testAttributeId, value: "Xanh dương", slug: "xanh-duong", sortOrder: 2 },
          { attributeId: testAttributeId, value: "Xanh lá", slug: "xanh-la", sortOrder: 3 },
          { attributeId: testAttributeId, value: "Vàng", slug: "vang", sortOrder: 4 },
          { attributeId: testAttributeId, value: "Đen", slug: "den", sortOrder: 5 },
        ],
      });
    });

    it("should handle pagination", async () => {
      const request = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values?page=1&limit=3`);
      const response = await GET(request, { params: { id: testAttributeId } });
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.data).toHaveLength(3);
      expect(data.pagination.page).toBe(1);
      expect(data.pagination.limit).toBe(3);
      expect(data.pagination.total).toBe(5);
      expect(data.pagination.pages).toBe(2);
    });

    it("should handle search", async () => {
      const request = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values?search=xanh`);
      const response = await GET(request, { params: { id: testAttributeId } });
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.data).toHaveLength(2);
      expect(data.data.every((value: any) => value.value.toLowerCase().includes("xanh"))).toBe(true);
    });

    it("should sort by different fields", async () => {
      const request = new NextRequest(`http://localhost:3000/api/admin/attributes/${testAttributeId}/values?sortBy=value&sortOrder=desc`);
      const response = await GET(request, { params: { id: testAttributeId } });
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.data[0].value).toBe("Đỏ");
      expect(data.data[1].value).toBe("Đen");
    });
  });
});
