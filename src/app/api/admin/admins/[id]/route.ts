import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import bcrypt from "bcryptjs";
import { z } from "zod";

const updateAdminSchema = z.object({
  name: z.string().min(2, "Tên phải có ít nhất 2 ký tự").optional(),
  email: z.string().email("Email không hợp lệ").optional(),
  password: z.string().min(6, "<PERSON>ật khẩu phải có ít nhất 6 ký tự").optional(),
  role: z.enum(["ADMIN", "MODERATOR"]).optional(),
  phone: z.string().optional(),
  department: z.string().optional(),
  isActive: z.boolean().optional(),
  permissions: z.record(z.boolean()).optional(),
});

// GET /api/admin/admins/[id] - Get specific admin user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Users can view their own profile, or ADMIN can view any admin
    if (session.user.id !== params.id && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền xem thông tin này" },
        { status: 403 }
      );
    }

    const adminUser = await prisma.adminUser.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        role: true,
        avatar: true,
        isActive: true,
        department: true,
        permissions: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
        createdByAdmin: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không tìm thấy quản trị viên" },
        { status: 404 }
      );
    }

    return NextResponse.json(adminUser);
  } catch (error) {
    console.error("Get admin user error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin quản trị viên" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/admins/[id] - Update admin user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const data = updateAdminSchema.parse(body);

    // Check if admin user exists
    const existingAdmin = await prisma.adminUser.findUnique({
      where: { id: params.id },
    });

    if (!existingAdmin) {
      return NextResponse.json(
        { error: "Không tìm thấy quản trị viên" },
        { status: 404 }
      );
    }

    // Permission checks
    const isSelfUpdate = session.user.id === params.id;
    const isAdmin = session.user.role === "ADMIN";

    // Users can update their own profile (except role and isActive)
    // Only ADMIN can update other users or change roles/status
    if (!isSelfUpdate && !isAdmin) {
      return NextResponse.json(
        { error: "Không có quyền cập nhật thông tin này" },
        { status: 403 }
      );
    }

    // Restrict what non-admin users can update about themselves
    if (isSelfUpdate && !isAdmin) {
      if (data.role || data.isActive !== undefined) {
        return NextResponse.json(
          {
            error: "Không thể thay đổi vai trò hoặc trạng thái của chính mình",
          },
          { status: 403 }
        );
      }
    }

    // Check email uniqueness if email is being updated
    if (data.email && data.email !== existingAdmin.email) {
      const emailExists = await prisma.adminUser.findUnique({
        where: { email: data.email },
      });

      if (emailExists) {
        return NextResponse.json(
          { error: "Email đã được sử dụng" },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};

    if (data.name) updateData.name = data.name;
    if (data.email) updateData.email = data.email;
    if (data.phone !== undefined) updateData.phone = data.phone;
    if (data.department !== undefined) updateData.department = data.department;
    if (data.permissions !== undefined)
      updateData.permissions = data.permissions;

    // Only admin can update these fields
    if (isAdmin) {
      if (data.role) updateData.role = data.role;
      if (data.isActive !== undefined) updateData.isActive = data.isActive;
    }

    // Hash password if provided
    if (data.password) {
      updateData.password = await bcrypt.hash(data.password, 12);
    }

    const updatedAdmin = await prisma.adminUser.update({
      where: { id: params.id },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        phone: true,
        department: true,
        isActive: true,
        updatedAt: true,
      },
    });

    return NextResponse.json({
      message: "Cập nhật thông tin thành công",
      adminUser: updatedAdmin,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Update admin user error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật thông tin" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/admins/[id] - Delete admin user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (
      !session ||
      session.user.type !== "admin" ||
      session.user.role !== "ADMIN"
    ) {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền xóa tài khoản quản trị viên" },
        { status: 403 }
      );
    }

    // Prevent self-deletion
    if (session.user.id === params.id) {
      return NextResponse.json(
        { error: "Không thể xóa tài khoản của chính mình" },
        { status: 400 }
      );
    }

    // Check if admin user exists
    const existingAdmin = await prisma.adminUser.findUnique({
      where: { id: params.id },
    });

    if (!existingAdmin) {
      return NextResponse.json(
        { error: "Không tìm thấy quản trị viên" },
        { status: 404 }
      );
    }

    await prisma.adminUser.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      message: "Xóa tài khoản quản trị viên thành công",
    });
  } catch (error) {
    console.error("Delete admin user error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa tài khoản" },
      { status: 500 }
    );
  }
}
