export default function SimpleAdminDashboard() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Tổng quan về hoạt động kinh doanh của cửa hàng
        </p>
      </div>

      {/* Simple Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Tổng doanh thu</h3>
          <p className="text-2xl font-bold text-gray-900">₫25,000,000</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Đ<PERSON><PERSON> hàng</h3>
          <p className="text-2xl font-bold text-gray-900">150</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Khách hàng</h3>
          <p className="text-2xl font-bold text-gray-900">89</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Sản phẩm</h3>
          <p className="text-2xl font-bold text-gray-900">245</p>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Hoạt động gần đây</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between border-b pb-2">
            <span>Đơn hàng mới #1234</span>
            <span className="text-sm text-gray-500">2 phút trước</span>
          </div>
          <div className="flex items-center justify-between border-b pb-2">
            <span>Khách hàng mới đăng ký</span>
            <span className="text-sm text-gray-500">15 phút trước</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Sản phẩm được cập nhật</span>
            <span className="text-sm text-gray-500">1 giờ trước</span>
          </div>
        </div>
      </div>
    </div>
  );
}
