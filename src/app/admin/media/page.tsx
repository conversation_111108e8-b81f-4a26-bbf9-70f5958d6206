'use client';

import { useState } from 'react';
import { MediaManager } from '@/components/admin/MediaManager';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Image as ImageIcon } from 'lucide-react';

export default function MediaTestPage() {
  const [isManagerOpen, setIsManagerOpen] = useState(false);
  const [selectedUrl, setSelectedUrl] = useState('');

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
          <ImageIcon className="h-5 w-5 text-purple-600" />
        </div>
        <div>
          <h1 className="text-3xl font-bold">Media Manager Test</h1>
          <p className="text-muted-foreground">
            Test tính năng quản lý media với MinIO
          </p>
        </div>
      </div>

      {/* Test Card */}
      <Card>
        <CardHeader>
          <CardTitle>Test Media Manager</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={() => setIsManagerOpen(true)}
            className="bg-pink-600 hover:bg-pink-700"
          >
            <ImageIcon className="h-4 w-4 mr-2" />
            Mở Media Manager
          </Button>

          {selectedUrl && (
            <div className="space-y-2">
              <p className="font-medium">URL đã chọn:</p>
              <p className="text-sm text-muted-foreground break-all">
                {selectedUrl}
              </p>
              
              {selectedUrl.match(/\.(jpg|jpeg|png|gif|webp)$/i) && (
                <div className="mt-4">
                  <p className="font-medium mb-2">Preview:</p>
                  <img
                    src={selectedUrl}
                    alt="Selected"
                    className="max-w-md h-auto border rounded-lg"
                  />
                </div>
              )}
            </div>
          )}

          <MediaManager
            isOpen={isManagerOpen}
            onClose={() => setIsManagerOpen(false)}
            onSelect={(url) => {
              setSelectedUrl(url);
              setIsManagerOpen(false);
            }}
            selectedUrl={selectedUrl}
            folder="test"
          />
        </CardContent>
      </Card>
    </div>
  );
}
