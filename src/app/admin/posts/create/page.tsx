'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { PostForm } from '@/components/admin/PostForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileText } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

interface PostFormData {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  featured: boolean;
  featuredImage?: string;
  tags: string[];
  categoryId?: string;
}

export default function CreatePostPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: PostFormData) => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/admin/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(result.message || 'Tạo bài viết thành công');
        
        // Redirect based on status
        if (data.status === 'PUBLISHED') {
          router.push('/admin/posts');
        } else {
          router.push('/admin/posts');
        }
      } else {
        toast.error(result.error || 'Có lỗi xảy ra khi tạo bài viết');
      }
    } catch (error) {
      console.error('Create post error:', error);
      toast.error('Có lỗi xảy ra khi tạo bài viết');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/admin/posts">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
        </Link>
        
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
            <FileText className="h-5 w-5 text-pink-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Viết bài mới</h1>
            <p className="text-muted-foreground">
              Tạo bài viết mới cho blog và nội dung marketing
            </p>
          </div>
        </div>
      </div>

      {/* Form */}
      <PostForm 
        onSubmit={handleSubmit}
        isLoading={isLoading}
      />
    </div>
  );
}
