"use client";

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Edit,
  Trash2,
  FileText,
  Eye,
  Calendar,
  User,
  Tag as Tag<PERSON><PERSON>,
  Star,
} from "lucide-react";
import { toast } from "sonner";
import { AdminDataTable, AdminErrorState } from "@/lib/admin/components";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";
import { TableConfig, TableColumn, BulkAction } from "@/lib/admin/types";
import { PostsStats } from "@/components/admin/PostsStats";

interface Post {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  featured: boolean;
  featuredImage?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
  category?: {
    id: string;
    name: string;
    slug: string;
  };
}

export default function AdminPostsPage() {
  const [selectedItems, setSelectedItems] = useState<Post[]>([]);

  // Data fetching
  const {
    data: posts,
    loading,
    error,
    pagination,
    refresh,
    setParams,
    params,
  } = useAdminData<Post>({
    endpoint: "/api/admin/posts",
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const { remove, bulkDelete } = useAdminCrud("/api/admin/posts");

  // Handle delete post
  const handleDeletePost = async (post: Post) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa bài viết "${post.title}"?`)) {
      return;
    }

    const result = await remove(post.id);
    if (result) {
      toast.success("Xóa bài viết thành công");
      refresh();
    }
  };

  // Handle toggle featured
  const handleToggleFeatured = async (post: Post) => {
    try {
      const response = await fetch(
        `/api/admin/posts/${post.id}?action=toggle-featured`,
        {
          method: "PATCH",
        }
      );

      if (response.ok) {
        toast.success(
          post.featured ? "Đã bỏ đánh dấu nổi bật" : "Đã đánh dấu nổi bật"
        );
        refresh();
      } else {
        toast.error("Có lỗi xảy ra khi cập nhật");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi cập nhật");
    }
  };

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Table columns configuration
  const columns: TableColumn<Post>[] = [
    {
      key: "title",
      title: "Tiêu đề",
      dataIndex: "title",
      sortable: true,
      searchable: true,
      render: (value: string, record: Post) => (
        <div className="space-y-1">
          <div className="font-medium">{value}</div>
          <div className="text-sm text-muted-foreground line-clamp-2">
            {record.excerpt}
          </div>
          <div className="flex items-center gap-2">
            {record.featured && (
              <Badge
                variant="secondary"
                className="bg-yellow-100 text-yellow-800"
              >
                <Star className="h-3 w-3 mr-1" />
                Nổi bật
              </Badge>
            )}
            {record.tags.length > 0 && (
              <div className="flex items-center gap-1">
                <TagIcon className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  {record.tags.slice(0, 2).join(", ")}
                  {record.tags.length > 2 && "..."}
                </span>
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      key: "status",
      title: "Trạng thái",
      dataIndex: "status",
      sortable: true,
      render: (value: string) => {
        const getStatusConfig = (status: string) => {
          switch (status) {
            case "PUBLISHED":
              return {
                text: "Đã xuất bản",
                className: "bg-green-100 text-green-800",
              };
            case "DRAFT":
              return {
                text: "Bản nháp",
                className: "bg-yellow-100 text-yellow-800",
              };
            case "ARCHIVED":
              return {
                text: "Đã lưu trữ",
                className: "bg-gray-100 text-gray-800",
              };
            default:
              return { text: status, className: "bg-gray-100 text-gray-800" };
          }
        };
        const config = getStatusConfig(value);
        return (
          <Badge variant="secondary" className={config.className}>
            {config.text}
          </Badge>
        );
      },
    },
    {
      key: "author",
      title: "Tác giả",
      dataIndex: "author",
      render: (value: Post["author"]) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span>{value.name}</span>
        </div>
      ),
    },
    {
      key: "createdAt",
      title: "Ngày tạo",
      dataIndex: "createdAt",
      sortable: true,
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span>{formatDate(value)}</span>
        </div>
      ),
    },
  ];

  // Bulk actions
  const bulkActions: BulkAction<Post>[] = [
    {
      key: "publish",
      label: "Xuất bản",
      icon: Eye,
      action: async (items: Post[]) => {
        const ids = items.map((item) => item.id);
        try {
          const response = await fetch("/api/admin/posts", {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ ids, status: "PUBLISHED" }),
          });
          if (response.ok) {
            toast.success(`Đã xuất bản ${ids.length} bài viết`);
            refresh();
          }
        } catch (error) {
          toast.error("Có lỗi xảy ra");
        }
      },
    },
    {
      key: "draft",
      label: "Chuyển về bản nháp",
      icon: FileText,
      action: async (items: Post[]) => {
        const ids = items.map((item) => item.id);
        try {
          const response = await fetch("/api/admin/posts", {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ ids, status: "DRAFT" }),
          });
          if (response.ok) {
            toast.success(`Đã chuyển ${ids.length} bài viết về bản nháp`);
            refresh();
          }
        } catch (error) {
          toast.error("Có lỗi xảy ra");
        }
      },
    },
    {
      key: "delete",
      label: "Xóa",
      icon: Trash2,
      variant: "destructive",
      action: async (items: Post[]) => {
        if (!confirm(`Bạn có chắc chắn muốn xóa ${items.length} bài viết?`)) {
          return;
        }
        const result = await bulkDelete(items.map((item) => item.id));
        if (result) {
          toast.success(`Đã xóa ${items.length} bài viết`);
          refresh();
        }
      },
    },
  ];

  // Table configuration
  const tableConfig: TableConfig<Post> = {
    columns,
    rowKey: "id",
    selection: {
      enabled: true,
    },
    actions: {
      enabled: true,
      items: [
        {
          key: "edit",
          label: "Chỉnh sửa",
          icon: Edit,
          href: (record: Post) => `/admin/posts/${record.id}/edit`,
        },
        {
          key: "featured",
          label: (record: Post) =>
            record.featured ? "Bỏ nổi bật" : "Đánh dấu nổi bật",
          icon: Star,
          action: handleToggleFeatured,
        },
        {
          key: "delete",
          label: "Xóa",
          icon: Trash2,
          variant: "destructive",
          action: handleDeletePost,
        },
      ],
    },
    pagination: {
      enabled: true,
      pageSize: 20,
    },
    search: {
      enabled: true,
      placeholder: "Tìm kiếm bài viết...",
    },
    filters: [
      {
        key: "status",
        label: "Trạng thái",
        type: "select",
        options: [
          { label: "Tất cả", value: "" },
          { label: "Đã xuất bản", value: "PUBLISHED" },
          { label: "Bản nháp", value: "DRAFT" },
          { label: "Đã lưu trữ", value: "ARCHIVED" },
        ],
      },
      {
        key: "featured",
        label: "Nổi bật",
        type: "select",
        options: [
          { label: "Tất cả", value: "" },
          { label: "Nổi bật", value: "true" },
          { label: "Không nổi bật", value: "false" },
        ],
      },
    ],
    emptyText: "Chưa có bài viết nào",
  };

  // Handle error state
  if (error) {
    return (
      <AdminErrorState
        title="Có lỗi xảy ra"
        description="Không thể tải danh sách bài viết"
        onRetry={refresh}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý bài viết</h1>
          <p className="text-muted-foreground">
            Quản lý blog và nội dung marketing
          </p>
        </div>
        <Link href="/admin/posts/create">
          <Button className="bg-pink-600 hover:bg-pink-700">
            <Plus className="h-4 w-4 mr-2" />
            Viết bài mới
          </Button>
        </Link>
      </div>

      {/* Stats */}
      <PostsStats />

      {/* Data Table */}
      <AdminDataTable
        config={tableConfig}
        dataSource={posts}
        loading={loading}
        onRefresh={refresh}
        onSearch={(search) => setParams({ search })}
        onPageChange={(page) => setParams({ page })}
        selectedRows={selectedItems}
        onSelectionChange={setSelectedItems}
        bulkActions={bulkActions}
        pagination={pagination}
      />
    </div>
  );
}
