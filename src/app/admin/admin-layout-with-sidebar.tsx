"use client";

import { useState } from "react";
import { EnhancedSidebar } from "@/components/admin/enhanced-sidebar";
import { EnhancedAdminHeader } from "@/components/admin/enhanced-admin-header";

interface AdminLayoutWithSidebarProps {
  children: React.ReactNode;
  title?: string;
}

export default function AdminLayoutWithSidebar({
  children,
  title,
}: AdminLayoutWithSidebarProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div
        className={`
          fixed inset-y-0 left-0 z-50 lg:static lg:inset-0
          ${isSidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}
          transition-transform duration-300 ease-in-out
        `}
      >
        <EnhancedSidebar />
      </div>

      {/* Overlay for mobile */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <EnhancedAdminHeader
          title={title}
          onMenuClick={() => setIsSidebarOpen(!isSidebarOpen)}
        />
        <main className="flex-1 overflow-auto p-6">{children}</main>
      </div>
    </div>
  );
}
