"use client";

import React, { useState } from "react";
import {
  Search,
  Filter,
  RefreshCw,
  Download,
  Upload,
  Trash2,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import {
  AdminTableProps,
  TableColumn,
  TableAction,
  BulkAction,
} from "../types";

interface AdminDataTableProps<T> extends AdminTableProps<T> {
  onSearch?: (search: string) => void;
  onFilter?: (filters: Record<string, any>) => void;
  onSort?: (field: string, order: "asc" | "desc") => void;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  onSelectionChange?: (selectedRows: T[]) => void;
  bulkActions?: BulkAction<T>[];
  searchValue?: string;
  selectedRows?: T[];
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
  };
}

export function AdminDataTable<T = any>({
  config,
  dataSource,
  loading = false,
  onRefresh,
  onSearch,
  onFilter,
  onSort,
  onPageChange,
  onPageSizeChange,
  onSelectionChange,
  bulkActions = [],
  searchValue = "",
  selectedRows = [],
  pagination,
  className,
}: AdminDataTableProps<T>) {
  const [searchInput, setSearchInput] = useState(searchValue);

  const handleSearch = (value: string) => {
    setSearchInput(value);
    onSearch?.(value);
  };

  const handleSort = (column: TableColumn<T>) => {
    if (!column.sortable || !onSort) return;

    // Toggle sort order
    const currentOrder = "asc"; // You might want to track this in state
    const newOrder = currentOrder === "asc" ? "desc" : "asc";
    onSort(column.key, newOrder);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange?.(dataSource);
    } else {
      onSelectionChange?.([]);
    }
  };

  const handleRowSelect = (record: T, checked: boolean) => {
    if (checked) {
      onSelectionChange?.([...selectedRows, record]);
    } else {
      onSelectionChange?.(
        selectedRows.filter(
          (row) =>
            row[config.rowKey as keyof T] !== record[config.rowKey as keyof T]
        )
      );
    }
  };

  const isRowSelected = (record: T): boolean => {
    return selectedRows.some(
      (row) =>
        row[config.rowKey as keyof T] === record[config.rowKey as keyof T]
    );
  };

  const isAllSelected =
    dataSource.length > 0 &&
    dataSource.every((record) => isRowSelected(record));

  const isSomeSelected = selectedRows.length > 0 && !isAllSelected;

  const renderCell = (column: TableColumn<T>, record: T, index: number) => {
    if (column.render) {
      return column.render(record[column.dataIndex as keyof T], record, index);
    }

    const value = column.dataIndex
      ? record[column.dataIndex as keyof T]
      : record[column.key as keyof T];
    return value?.toString() || "";
  };

  const totalPages = pagination
    ? Math.ceil(pagination.total / pagination.pageSize)
    : 1;
  const currentPage = pagination?.current || 1;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header with search and actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {config.search?.enabled && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={config.search.placeholder || "Tìm kiếm..."}
                value={searchInput}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
          )}

          {config.filters && (
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Lọc
            </Button>
          )}
        </div>

        <div className="flex items-center gap-2">
          {selectedRows.length > 0 && bulkActions.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  Thao tác ({selectedRows.length})
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {bulkActions.map((action) => (
                  <DropdownMenuItem
                    key={action.key}
                    onClick={() => action.onClick(selectedRows)}
                    disabled={action.disabled?.(selectedRows)}
                  >
                    {action.icon}
                    {action.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
            >
              <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
            </Button>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              {config.selection?.enabled && (
                <TableHead className="w-12">
                  <Checkbox
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
              )}

              {config.columns.map((column) => (
                <TableHead
                  key={column.key}
                  className={cn(
                    column.className,
                    column.sortable && "cursor-pointer hover:bg-muted/50",
                    column.align === "center" && "text-center",
                    column.align === "right" && "text-right"
                  )}
                  style={{ width: column.width }}
                  onClick={() => handleSort(column)}
                >
                  {column.title}
                </TableHead>
              ))}

              {config.actions?.enabled && (
                <TableHead className="w-12"></TableHead>
              )}
            </TableRow>
          </TableHeader>

          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={
                    config.columns.length +
                    (config.selection?.enabled ? 1 : 0) +
                    (config.actions?.enabled ? 1 : 0)
                  }
                  className="text-center py-8"
                >
                  Đang tải...
                </TableCell>
              </TableRow>
            ) : dataSource.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={
                    config.columns.length +
                    (config.selection?.enabled ? 1 : 0) +
                    (config.actions?.enabled ? 1 : 0)
                  }
                  className="text-center py-8 text-muted-foreground"
                >
                  {config.emptyText || "Không có dữ liệu"}
                </TableCell>
              </TableRow>
            ) : (
              dataSource.map((record, index) => (
                <TableRow key={record[config.rowKey as keyof T]?.toString()}>
                  {config.selection?.enabled && (
                    <TableCell>
                      <Checkbox
                        checked={isRowSelected(record)}
                        onCheckedChange={(checked) =>
                          handleRowSelect(record, !!checked)
                        }
                      />
                    </TableCell>
                  )}

                  {config.columns.map((column) => (
                    <TableCell
                      key={column.key}
                      className={cn(
                        column.className,
                        column.align === "center" && "text-center",
                        column.align === "right" && "text-right"
                      )}
                    >
                      {renderCell(column, record, index)}
                    </TableCell>
                  ))}

                  {config.actions?.enabled && (
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {config.actions.items
                            .filter(
                              (action) => action.visible?.(record) !== false
                            )
                            .map((action) => (
                              <DropdownMenuItem
                                key={action.key}
                                onClick={() => action.onClick(record)}
                                disabled={action.disabled?.(record)}
                              >
                                {action.icon}
                                {action.label}
                              </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {config.pagination?.enabled && pagination && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Hiển thị {(currentPage - 1) * pagination.pageSize + 1} đến{" "}
            {Math.min(currentPage * pagination.pageSize, pagination.total)}{" "}
            trong tổng số {pagination.total} kết quả
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(1)}
              disabled={currentPage === 1}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <span className="text-sm">
              Trang {currentPage} / {totalPages}
            </span>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(totalPages)}
              disabled={currentPage === totalPages}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
