import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Page validation schema
export const pageSchema = z.object({
  title: z.string().min(1, "Tiêu đề là bắt buộc"),
  content: z.string().min(1, "Nội dung là bắt buộc"),
  excerpt: z.string().optional(),
  slug: z.string().optional(),
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).default("DRAFT"),
  featured: z.boolean().default(false),
  featuredImage: z.string().optional(),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  authorId: z.string().min(1, "Tác giả là bắt buộc"),
});

export type PageData = z.infer<typeof pageSchema>;

export class PageController {
  // Get all pages with pagination and filters
  async getAll(params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    featured?: boolean;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }) {
    try {
      const {
        page = 1,
        limit = 10,
        search = "",
        status,
        featured,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = params;

      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      if (search) {
        where.OR = [
          { title: { contains: search, mode: "insensitive" } },
          { content: { contains: search, mode: "insensitive" } },
          { excerpt: { contains: search, mode: "insensitive" } },
        ];
      }

      if (status) {
        where.status = status;
      }

      if (featured !== undefined) {
        where.featured = featured;
      }

      // Get pages with pagination
      const [pages, total] = await Promise.all([
        prisma.page.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        }),
        prisma.page.count({ where }),
      ]);

      return {
        success: true,
        data: pages,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error("Get pages error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi tải danh sách trang",
      };
    }
  }

  // Get single page by ID
  async get(id: string) {
    try {
      const page = await prisma.page.findUnique({
        where: { id },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!page) {
        return {
          success: false,
          error: "Không tìm thấy trang",
        };
      }

      return {
        success: true,
        data: page,
      };
    } catch (error) {
      console.error("Get page error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi tải trang",
      };
    }
  }

  // Create new page
  async create(data: PageData) {
    try {
      // Validate data
      const validatedData = pageSchema.parse(data);

      // Generate slug if not provided
      if (!validatedData.slug) {
        validatedData.slug = validatedData.title
          .toLowerCase()
          .normalize("NFD")
          .replace(/[\u0300-\u036f]/g, "")
          .replace(/[^a-z0-9\s-]/g, "")
          .replace(/\s+/g, "-")
          .replace(/-+/g, "-")
          .trim();
      }

      // Check if slug already exists
      const existingPage = await prisma.page.findUnique({
        where: { slug: validatedData.slug },
      });

      if (existingPage) {
        // Add timestamp to make slug unique
        validatedData.slug = `${validatedData.slug}-${Date.now()}`;
      }

      // Create page
      const page = await prisma.page.create({
        data: validatedData,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return {
        success: true,
        data: page,
        message: "Tạo trang thành công",
      };
    } catch (error) {
      console.error("Create page error:", error);
      
      if (error instanceof z.ZodError) {
        return {
          success: false,
          error: error.errors[0]?.message || "Dữ liệu không hợp lệ",
        };
      }

      return {
        success: false,
        error: "Có lỗi xảy ra khi tạo trang",
      };
    }
  }

  // Update page
  async update(id: string, data: Partial<PageData>) {
    try {
      // Check if page exists
      const existingPage = await prisma.page.findUnique({
        where: { id },
      });

      if (!existingPage) {
        return {
          success: false,
          error: "Không tìm thấy trang",
        };
      }

      // Validate data (partial)
      const validatedData = pageSchema.partial().parse(data);

      // If slug is being updated, check for uniqueness
      if (validatedData.slug && validatedData.slug !== existingPage.slug) {
        const slugExists = await prisma.page.findFirst({
          where: {
            slug: validatedData.slug,
            id: { not: id },
          },
        });

        if (slugExists) {
          return {
            success: false,
            error: "Slug đã tồn tại",
          };
        }
      }

      // Update page
      const page = await prisma.page.update({
        where: { id },
        data: validatedData,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return {
        success: true,
        data: page,
        message: "Cập nhật trang thành công",
      };
    } catch (error) {
      console.error("Update page error:", error);
      
      if (error instanceof z.ZodError) {
        return {
          success: false,
          error: error.errors[0]?.message || "Dữ liệu không hợp lệ",
        };
      }

      return {
        success: false,
        error: "Có lỗi xảy ra khi cập nhật trang",
      };
    }
  }

  // Delete page
  async delete(id: string) {
    try {
      // Check if page exists
      const existingPage = await prisma.page.findUnique({
        where: { id },
      });

      if (!existingPage) {
        return {
          success: false,
          error: "Không tìm thấy trang",
        };
      }

      // Delete page
      await prisma.page.delete({
        where: { id },
      });

      return {
        success: true,
        message: "Xóa trang thành công",
      };
    } catch (error) {
      console.error("Delete page error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi xóa trang",
      };
    }
  }

  // Bulk delete pages
  async bulkDelete(ids: string[]) {
    try {
      const result = await prisma.page.deleteMany({
        where: {
          id: { in: ids },
        },
      });

      return {
        success: true,
        message: `Đã xóa ${result.count} trang`,
      };
    } catch (error) {
      console.error("Bulk delete pages error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi xóa trang",
      };
    }
  }

  // Toggle featured status
  async toggleFeatured(id: string) {
    try {
      const page = await prisma.page.findUnique({
        where: { id },
      });

      if (!page) {
        return {
          success: false,
          error: "Không tìm thấy trang",
        };
      }

      const updatedPage = await prisma.page.update({
        where: { id },
        data: { featured: !page.featured },
      });

      return {
        success: true,
        data: updatedPage,
        message: updatedPage.featured
          ? "Đã đánh dấu trang nổi bật"
          : "Đã bỏ đánh dấu trang nổi bật",
      };
    } catch (error) {
      console.error("Toggle featured error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi cập nhật trạng thái nổi bật",
      };
    }
  }
}
