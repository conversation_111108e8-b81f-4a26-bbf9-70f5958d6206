import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { NextRequest } from "next/server";

// Post validation schema
export const postSchema = z.object({
  title: z.string().min(1, "Tiêu đề là bắt buộc"),
  content: z.string().min(1, "Nội dung là bắt buộc"),
  excerpt: z.string().optional(),
  slug: z.string().optional(),
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).default("DRAFT"),
  featured: z.boolean().default(false),
  featuredImage: z.string().optional(),
  tags: z.array(z.string()).default([]),
  categoryId: z.string().optional(),
  authorId: z.string().min(1, "Tác giả là bắt buộc"),
});

export const updatePostSchema = postSchema.partial().omit({ authorId: true });

export interface PostFormData {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  featured: boolean;
  featuredImage?: string;
  tags: string[];
  categoryId?: string;
  authorId: string;
}

export interface PostListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  featured?: boolean;
  categoryId?: string;
  authorId?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export class PostController {
  /**
   * Generate slug from title
   */
  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "") // Remove diacritics
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single
      .trim();
  }

  /**
   * Ensure unique slug
   */
  private async ensureUniqueSlug(
    baseSlug: string,
    excludeId?: string
  ): Promise<string> {
    let slug = baseSlug;
    let counter = 1;

    while (true) {
      const existing = await prisma.post.findUnique({
        where: { slug },
      });

      if (!existing || (excludeId && existing.id === excludeId)) {
        break;
      }

      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  /**
   * Get posts with pagination and filters
   */
  async list(params: PostListParams = {}) {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      featured,
      categoryId,
      authorId,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = params;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { excerpt: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (featured !== undefined) {
      where.featured = featured;
    }

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (authorId) {
      where.authorId = authorId;
    }

    // Get total count
    const total = await prisma.post.count({ where });

    // Get posts
    const posts = await prisma.post.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sortBy]: sortOrder },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    return {
      success: true,
      data: posts,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Get single post by ID
   */
  async get(id: string) {
    const post = await prisma.post.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!post) {
      return {
        success: false,
        error: "Không tìm thấy bài viết",
        data: null,
      };
    }

    return {
      success: true,
      data: post,
    };
  }

  /**
   * Create new post
   */
  async create(data: PostFormData) {
    try {
      // Validate data
      const validatedData = postSchema.parse(data);

      // Generate slug if not provided
      let slug = validatedData.slug;
      if (!slug) {
        slug = this.generateSlug(validatedData.title);
      }

      // Ensure unique slug
      slug = await this.ensureUniqueSlug(slug);

      // Generate excerpt if not provided
      let excerpt = validatedData.excerpt;
      if (!excerpt && validatedData.content) {
        // Extract first 200 characters from content (remove HTML tags)
        excerpt = validatedData.content
          .replace(/<[^>]*>/g, "") // Remove HTML tags
          .substring(0, 200)
          .trim();
        if (excerpt.length === 200) {
          excerpt += "...";
        }
      }

      // Validate category if provided
      if (validatedData.categoryId) {
        const category = await prisma.category.findUnique({
          where: { id: validatedData.categoryId },
        });

        if (!category) {
          return {
            success: false,
            error: "Danh mục không tồn tại",
            data: null,
          };
        }
      }

      // Validate author
      const author = await prisma.adminUser.findUnique({
        where: { id: validatedData.authorId },
      });

      if (!author) {
        return {
          success: false,
          error: "Tác giả không tồn tại",
          data: null,
        };
      }

      // Create post
      const post = await prisma.post.create({
        data: {
          ...validatedData,
          slug,
          excerpt,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      return {
        success: true,
        data: post,
        message: "Tạo bài viết thành công",
      };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          error: error.errors[0].message,
          data: null,
        };
      }

      console.error("Create post error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi tạo bài viết",
        data: null,
      };
    }
  }

  /**
   * Update post
   */
  async update(id: string, data: Partial<PostFormData>) {
    try {
      // Check if post exists
      const existingPost = await prisma.post.findUnique({
        where: { id },
      });

      if (!existingPost) {
        return {
          success: false,
          error: "Không tìm thấy bài viết",
          data: null,
        };
      }

      // Validate data
      const validatedData = updatePostSchema.parse(data);

      // Handle slug update
      let slug = validatedData.slug;
      if (validatedData.title && (!slug || slug === existingPost.slug)) {
        slug = this.generateSlug(validatedData.title);
      }

      if (slug && slug !== existingPost.slug) {
        slug = await this.ensureUniqueSlug(slug, id);
      }

      // Generate excerpt if title or content changed
      let excerpt = validatedData.excerpt;
      if (validatedData.content && !excerpt) {
        excerpt = validatedData.content
          .replace(/<[^>]*>/g, "")
          .substring(0, 200)
          .trim();
        if (excerpt.length === 200) {
          excerpt += "...";
        }
      }

      // Validate category if provided
      if (validatedData.categoryId) {
        const category = await prisma.category.findUnique({
          where: { id: validatedData.categoryId },
        });

        if (!category) {
          return {
            success: false,
            error: "Danh mục không tồn tại",
            data: null,
          };
        }
      }

      // Update post
      const updateData: any = { ...validatedData };
      if (slug) updateData.slug = slug;
      if (excerpt) updateData.excerpt = excerpt;

      const post = await prisma.post.update({
        where: { id },
        data: updateData,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      return {
        success: true,
        data: post,
        message: "Cập nhật bài viết thành công",
      };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          error: error.errors[0].message,
          data: null,
        };
      }

      console.error("Update post error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi cập nhật bài viết",
        data: null,
      };
    }
  }

  /**
   * Delete post
   */
  async delete(id: string) {
    try {
      // Check if post exists
      const existingPost = await prisma.post.findUnique({
        where: { id },
      });

      if (!existingPost) {
        return {
          success: false,
          error: "Không tìm thấy bài viết",
          data: null,
        };
      }

      // Delete post
      await prisma.post.delete({
        where: { id },
      });

      return {
        success: true,
        message: "Xóa bài viết thành công",
      };
    } catch (error) {
      console.error("Delete post error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi xóa bài viết",
        data: null,
      };
    }
  }

  /**
   * Bulk delete posts
   */
  async bulkDelete(ids: string[]) {
    try {
      if (!ids || ids.length === 0) {
        return {
          success: false,
          error: "Không có bài viết nào được chọn",
          data: null,
        };
      }

      // Delete posts
      const result = await prisma.post.deleteMany({
        where: {
          id: {
            in: ids,
          },
        },
      });

      return {
        success: true,
        message: `Đã xóa ${result.count} bài viết`,
        data: { deletedCount: result.count },
      };
    } catch (error) {
      console.error("Bulk delete posts error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi xóa bài viết",
        data: null,
      };
    }
  }

  /**
   * Bulk update status
   */
  async bulkUpdateStatus(
    ids: string[],
    status: "DRAFT" | "PUBLISHED" | "ARCHIVED"
  ) {
    try {
      if (!ids || ids.length === 0) {
        return {
          success: false,
          error: "Không có bài viết nào được chọn",
          data: null,
        };
      }

      // Update posts status
      const result = await prisma.post.updateMany({
        where: {
          id: {
            in: ids,
          },
        },
        data: {
          status,
        },
      });

      const statusText = {
        DRAFT: "Bản nháp",
        PUBLISHED: "Đã xuất bản",
        ARCHIVED: "Đã lưu trữ",
      }[status];

      return {
        success: true,
        message: `Đã cập nhật ${result.count} bài viết thành ${statusText}`,
        data: { updatedCount: result.count },
      };
    } catch (error) {
      console.error("Bulk update status error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi cập nhật trạng thái",
        data: null,
      };
    }
  }

  /**
   * Toggle featured status
   */
  async toggleFeatured(id: string) {
    try {
      // Get current post
      const post = await prisma.post.findUnique({
        where: { id },
        select: { featured: true },
      });

      if (!post) {
        return {
          success: false,
          error: "Không tìm thấy bài viết",
          data: null,
        };
      }

      // Toggle featured status
      const updatedPost = await prisma.post.update({
        where: { id },
        data: {
          featured: !post.featured,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      return {
        success: true,
        data: updatedPost,
        message: updatedPost.featured
          ? "Đã đánh dấu bài viết nổi bật"
          : "Đã bỏ đánh dấu bài viết nổi bật",
      };
    } catch (error) {
      console.error("Toggle featured error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi cập nhật trạng thái nổi bật",
        data: null,
      };
    }
  }

  /**
   * Get posts statistics
   */
  async getStats() {
    try {
      const [total, published, draft, archived, featured] = await Promise.all([
        prisma.post.count(),
        prisma.post.count({ where: { status: "PUBLISHED" } }),
        prisma.post.count({ where: { status: "DRAFT" } }),
        prisma.post.count({ where: { status: "ARCHIVED" } }),
        prisma.post.count({ where: { featured: true } }),
      ]);

      return {
        success: true,
        data: {
          total,
          published,
          draft,
          archived,
          featured,
        },
      };
    } catch (error) {
      console.error("Get posts stats error:", error);
      return {
        success: false,
        error: "Có lỗi xảy ra khi lấy thống kê",
        data: null,
      };
    }
  }
}
