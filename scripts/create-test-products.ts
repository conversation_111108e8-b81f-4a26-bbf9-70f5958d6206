import { PrismaClient, ProductStatus } from "@prisma/client";

const prisma = new PrismaClient();

async function createTestProducts() {
  try {
    console.log("🔍 Creating test products...");

    // Get or create categories first
    let categories = await prisma.category.findMany();

    if (categories.length === 0) {
      console.log("Creating test categories...");
      await prisma.category.createMany({
        data: [
          {
            name: "Thời trang nam",
            slug: "thoi-trang-nam",
            description: "Quần áo và phụ kiện dành cho nam giới",
            image: "https://via.placeholder.com/300x200",
          },
          {
            name: "Thời trang nữ",
            slug: "thoi-trang-nu",
            description: "Quần áo và phụ kiện dành cho nữ giới",
            image: "https://via.placeholder.com/300x200",
          },
          {
            name: "<PERSON><PERSON> kiệ<PERSON>",
            slug: "phu-kien",
            description: "<PERSON><PERSON> kiện thời trang",
            image: "https://via.placeholder.com/300x200",
          },
        ],
      });

      // Fetch created categories
      categories = await prisma.category.findMany();
      console.log(`✅ Created ${categories.length} categories`);
    }

    // Check if products already exist
    const existingProducts = await prisma.product.findMany();
    if (existingProducts.length > 0) {
      console.log(`✅ Already have ${existingProducts.length} products`);
      return;
    }

    // Create test products
    const testProducts = [
      {
        name: "Áo thun nam basic",
        slug: "ao-thun-nam-basic",
        description:
          "Áo thun nam chất liệu cotton 100%, form regular fit, thoải mái cho mọi hoạt động",
        price: 299000,
        salePrice: 199000,
        sku: "TSH-001",
        stock: 50,
        categoryId: categories[0].id,
        images: ["https://via.placeholder.com/400x400"],
        featured: true,
        status: ProductStatus.ACTIVE,
        tags: ["áo thun", "nam", "cotton", "basic"],
      },
      {
        name: "Váy maxi nữ",
        slug: "vay-maxi-nu",
        description:
          "Váy maxi dài tay, chất liệu voan mềm mại, phù hợp cho dạo phố và dự tiệc",
        price: 599000,
        salePrice: null,
        sku: "DRS-001",
        stock: 30,
        categoryId: categories[1].id,
        images: ["https://via.placeholder.com/400x400"],
        featured: false,
        status: ProductStatus.ACTIVE,
        tags: ["váy", "nữ", "maxi", "voan"],
      },
      {
        name: "Quần jeans nam",
        slug: "quan-jeans-nam",
        description:
          "Quần jeans nam form slim fit, chất liệu denim cao cấp, bền đẹp theo thời gian",
        price: 799000,
        salePrice: 599000,
        sku: "JNS-001",
        stock: 25,
        categoryId: categories[0].id,
        images: ["https://via.placeholder.com/400x400"],
        featured: true,
        status: ProductStatus.ACTIVE,
        tags: ["quần", "jeans", "nam", "slim fit"],
      },
      {
        name: "Túi xách nữ",
        slug: "tui-xach-nu",
        description:
          "Túi xách nữ da thật, thiết kế sang trọng, nhiều ngăn tiện dụng",
        price: 1299000,
        salePrice: null,
        sku: "BAG-001",
        stock: 15,
        categoryId: categories[2].id,
        images: ["https://via.placeholder.com/400x400"],
        featured: false,
        status: ProductStatus.ACTIVE,
        tags: ["túi xách", "nữ", "da thật", "sang trọng"],
      },
      {
        name: "Giày sneaker unisex",
        slug: "giay-sneaker-unisex",
        description:
          "Giày sneaker unisex, thiết kế hiện đại, phù hợp cho cả nam và nữ",
        price: 899000,
        salePrice: 699000,
        sku: "SNK-001",
        stock: 40,
        categoryId: categories[2].id,
        images: ["https://via.placeholder.com/400x400"],
        featured: true,
        status: ProductStatus.ACTIVE,
        tags: ["giày", "sneaker", "unisex", "hiện đại"],
      },
    ];

    console.log("Creating test products...");
    for (const productData of testProducts) {
      await prisma.product.create({
        data: productData,
      });
    }

    console.log(`✅ Created ${testProducts.length} test products`);

    // Verify products were created
    const totalProducts = await prisma.product.count();
    console.log(`✅ Total products in database: ${totalProducts}`);
  } catch (error) {
    console.error("❌ Error creating test products:", error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestProducts();
