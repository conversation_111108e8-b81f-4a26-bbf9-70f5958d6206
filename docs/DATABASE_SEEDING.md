# Database Seeding Guide

## Tổng quan

Database seeding đã được thống nhất và rút gọn để dễ dàng sử dụng và bảo trì. Chỉ cần một command duy nhất để seed toàn bộ database.

## Commands

### Seed Database

```bash
npm run db:seed
```

Command này sẽ:

1. Tạo admin users và demo user
2. Seed basic settings
3. Tạo categories cơ bản
4. Tạo một số sample products

### Reset và Seed lại

```bash
npm run db:reset
```

Command này sẽ:

1. Reset database về trạng thái clean
2. Chạy lại toàn bộ quá trình seed

## Cấu trúc Seed

### 1. Basic Data (seedBasicData)

- **Admin User**: <EMAIL> / admin123
- **Moderator User**: <EMAIL> / moderator123
- **Demo User**: <EMAIL> / user123

### 2. Settings (seedSettings)

- Site name, description
- Contact info
- Currency, shipping settings

### 3. Categories (seedCategories)

- Thời trang nữ/nam
- Giày dép
- Subcategories tương ứng

### 4. Sample Products (seedSampleProducts)

- Một số sản phẩm mẫu để demo
- Với images, pricing, stock

## Tùy chỉnh

Để thay đổi data được seed, chỉnh sửa trực tiếp trong `prisma/seed.ts`:

```typescript
// Thêm categories mới
const categories = [
  {
    name: "Danh mục mới",
    slug: "danh-muc-moi",
    // ...
  },
];

// Thêm products mới
const products = [
  {
    name: "Sản phẩm mới",
    slug: "san-pham-moi",
    // ...
  },
];
```

## Migration từ hệ thống cũ

### Đã loại bỏ

- `generates/` → Quá phức tạp, không cần thiết
- `scripts/` → Đã integrate vào seed.ts
- `prisma/seed-products.ts` → Integrate vào seed.ts
- `prisma/seed-settings.ts` → Integrate vào seed.ts
- Multiple generate commands → Chỉ còn db:seed

### Lý do loại bỏ generates system

- Quá phức tạp cho nhu cầu đơn giản
- Có quá nhiều dependencies (@faker-js/faker, commander, inquirer, etc.)
- Khó maintain và debug
- Basic seeding đã đủ cho development

## Troubleshooting

### Lỗi khi seed

```bash
# Check database connection
npm run p:s

# Reset và seed lại
npm run db:reset
```

### Cần thêm data

Chỉnh sửa trực tiếp trong `prisma/seed.ts` để thêm:

- Categories mới
- Products mới
- Settings mới
- Users mới
