# Tài liệu Testing cho Hệ thống Attributes

## Tổng quan

Tài liệu này mô tả chi tiết về việc testing hệ thống attributes trong NS Shop, bao gồm các loại test khác nhau, cách chạy tests, và các kịch bản test quan trọng.

## Cấu trúc Testing

Hệ thống attributes được test ở nhiều cấp độ khác nhau:

### 1. Unit Tests
- **Vị trí**: `tests/unit/`
- **Mục đích**: Test các function, utility, và logic riêng lẻ
- **Công cụ**: Jest
- **Coverage**: Models, API routes, hooks, utilities

### 2. Integration Tests
- **Vị trí**: `tests/integration/`
- **Mục đích**: Test tương tác giữa các components và API
- **Công cụ**: Jest với database test
- **Coverage**: API endpoints, database operations

### 3. End-to-End Tests
- **Vị trí**: `tests/e2e/`
- **<PERSON><PERSON><PERSON> đích**: Test toàn bộ user flow từ UI đến database
- **Công cụ**: Playwright
- **Coverage**: CRUD operations, user interactions

## Cấu trúc Thư mục

```
tests/
├── unit/
│   ├── lib/utils/attributes.test.ts
│   ├── api/admin/attributes.test.ts
│   └── models/attribute.test.ts
├── integration/
│   ├── api/admin/attributes.test.ts
│   └── api/admin/attribute-values.test.ts
└── e2e/
    └── admin/attributes/
        ├── attributes-list.spec.ts
        ├── create-attribute.spec.ts
        ├── edit-attribute.spec.ts
        └── attribute-detail.spec.ts
```

## Các Loại Attributes được Test

### 1. TEXT Attributes
- Thuộc tính văn bản tự do
- Không cần values
- Test: validation, creation, update

### 2. COLOR Attributes
- Thuộc tính màu sắc
- Cần predefined values
- Test: color picker, value management

### 3. SIZE Attributes
- Thuộc tính kích thước
- Cần predefined values
- Test: size selection, ordering

### 4. SELECT Attributes
- Thuộc tính lựa chọn đơn
- Cần predefined values
- Test: dropdown selection

### 5. MULTI_SELECT Attributes
- Thuộc tính lựa chọn nhiều
- Cần predefined values
- Test: multiple selection

### 6. BOOLEAN Attributes
- Thuộc tính có/không
- Không cần values
- Test: toggle functionality

### 7. NUMBER Attributes
- Thuộc tính số
- Không cần values
- Test: number validation

## Kịch bản Test Chính

### CRUD Operations
1. **Create (Tạo)**
   - Tạo attribute với thông tin hợp lệ
   - Tạo attribute với values
   - Validation các trường bắt buộc
   - Kiểm tra duplicate slug

2. **Read (Đọc)**
   - Hiển thị danh sách attributes
   - Xem chi tiết attribute
   - Filtering và searching
   - Pagination

3. **Update (Cập nhật)**
   - Cập nhật thông tin attribute
   - Thêm/sửa/xóa values
   - Reorder values
   - Validation updates

4. **Delete (Xóa)**
   - Xóa attribute không sử dụng
   - Ngăn xóa attribute đang sử dụng
   - Cascade delete values

### Business Logic Tests
1. **Slug Generation**
   - Auto-generate từ tên tiếng Việt
   - Handle special characters
   - Ensure uniqueness

2. **Value Management**
   - Add/edit/delete values
   - Drag & drop reordering
   - Duplicate prevention

3. **Type-specific Behavior**
   - Show/hide values section
   - Type-specific validation
   - UI adaptations

4. **Usage Tracking**
   - Count products using attribute
   - Prevent deletion when in use
   - Show usage warnings

## Test Data

### Sample Attributes
```javascript
const testAttributes = [
  {
    name: "Màu sắc",
    slug: "mau-sac",
    type: "COLOR",
    values: ["Đỏ", "Xanh", "Vàng"]
  },
  {
    name: "Kích thước",
    slug: "kich-thuoc", 
    type: "SIZE",
    values: ["S", "M", "L", "XL"]
  },
  {
    name: "Chất liệu",
    slug: "chat-lieu",
    type: "SELECT",
    values: ["Cotton", "Polyester", "Silk"]
  }
];
```

### Test Users
- **Admin User**: Full access to attributes management
- **Regular User**: No access (should get 401)

## Môi trường Test

### Database
- Sử dụng test database riêng biệt
- Auto cleanup sau mỗi test
- Seed data cho integration tests

### API Mocking
- Mock external services
- Simulate error conditions
- Control response timing

### Browser Testing
- Multiple browsers (Chrome, Firefox, Safari)
- Different screen sizes
- Mobile responsive testing

## Metrics và Coverage

### Coverage Targets
- **Unit Tests**: 90%+ coverage
- **Integration Tests**: 80%+ API coverage
- **E2E Tests**: 100% critical user flows

### Performance Metrics
- API response time < 200ms
- Page load time < 2s
- Test execution time < 30s per suite

## Troubleshooting

### Common Issues
1. **Database Connection**: Ensure test DB is running
2. **Port Conflicts**: Use different ports for test server
3. **Async Issues**: Proper await/async handling
4. **Race Conditions**: Use proper test isolation

### Debug Tips
1. Use `console.log` for debugging
2. Check browser dev tools in E2E tests
3. Verify database state after tests
4. Use test.only() for focused testing

## Best Practices

### Test Writing
1. **Descriptive Names**: Clear test descriptions
2. **Arrange-Act-Assert**: Follow AAA pattern
3. **Independent Tests**: No test dependencies
4. **Clean State**: Reset between tests

### Data Management
1. **Minimal Data**: Only create needed data
2. **Cleanup**: Always cleanup after tests
3. **Realistic Data**: Use realistic Vietnamese data
4. **Edge Cases**: Test boundary conditions

### Maintenance
1. **Regular Updates**: Keep tests updated with code changes
2. **Refactoring**: Extract common test utilities
3. **Documentation**: Keep docs updated
4. **Review**: Regular test review and optimization

## Liên kết Tài liệu

- [Hướng dẫn chạy Unit Tests](./unit-tests.md)
- [Hướng dẫn chạy Integration Tests](./integration-tests.md)
- [Hướng dẫn chạy E2E Tests](./e2e-tests.md)
- [Test Scenarios chi tiết](./test-scenarios.md)
- [Troubleshooting Guide](./troubleshooting.md)
