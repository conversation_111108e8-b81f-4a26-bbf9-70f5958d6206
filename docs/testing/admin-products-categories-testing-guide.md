# Hướng Dẫn Testing Admin Products và Categories

## Tổng Quan

Tài liệu này cung cấp hướng dẫn chi tiết về việc testing các chức năng quản lý sản phẩm và danh mục trong admin panel của NS Shop. Chúng ta sử dụng Playwright để thực hiện End-to-End (E2E) testing.

## Cấu Trúc Testing

### 1. Test Files

```
tests/
├── e2e/
│   ├── admin-products.spec.ts     # Tests cho quản lý sản phẩm
│   └── admin-categories.spec.ts   # Tests cho quản lý danh mục
├── helpers/
│   └── admin-helpers.ts           # Helper functions cho admin testing
└── fixtures/
    └── admin-test-data.ts         # Test data và fixtures
```

### 2. Test Categories

#### Admin Products Testing

- **Danh sách sản phẩm**: <PERSON><PERSON><PERSON> thị, ph<PERSON> trang, t<PERSON><PERSON> kiếm, sắp xếp
- **Tạo sản phẩm**: Form validation, upload hình ảnh, thêm tags
- **Chỉnh sửa sản phẩm**: <PERSON><PERSON><PERSON> nh<PERSON><PERSON> thông tin, thay đổi trạng thái
- **Xóa sản phẩm**: Xóa đơn lẻ, xóa hàng loạt
- **Quản lý trạng thái**: Active, Inactive, Out of Stock
- **Tìm kiếm và lọc**: Theo tên, danh mục, trạng thái

#### Admin Categories Testing

- **Danh sách danh mục**: Hiển thị cây phân cấp, thống kê
- **Tạo danh mục**: Form validation, chọn danh mục cha
- **Chỉnh sửa danh mục**: Cập nhật thông tin, thay đổi cấu trúc
- **Xóa danh mục**: Kiểm tra ràng buộc với sản phẩm và danh mục con
- **Quản lý phân cấp**: Parent-child relationships, depth levels
- **Tìm kiếm**: Theo tên danh mục

## Cách Chạy Tests

### 1. Chuẩn Bị Môi Trường

```bash
# Cài đặt dependencies
npm install

# Cài đặt Playwright browsers
npx playwright install

# Khởi động development server
npm run dev
```

### 2. Chạy Tests

```bash
# Chạy tất cả admin tests
npx playwright test tests/e2e/admin-*.spec.ts

# Chạy tests cho products
npx playwright test tests/e2e/admin-products.spec.ts

# Chạy tests cho categories
npx playwright test tests/e2e/admin-categories.spec.ts

# Chạy tests với UI mode
npx playwright test --ui

# Chạy tests với debug mode
npx playwright test --debug
```

### 3. Xem Kết Quả

```bash
# Mở HTML report
npx playwright show-report
```

## Test Data và Fixtures

### 1. Admin Users

```typescript
const adminUsers = {
  mainAdmin: {
    email: "<EMAIL>",
    password: "admin123",
    name: "Main Admin",
    role: "ADMIN",
  },
};
```

### 2. Test Products

```typescript
const testProducts = {
  tshirt: {
    name: "Áo thun nam basic",
    description: "Áo thun nam chất liệu cotton 100%",
    price: 299000,
    salePrice: 199000,
    sku: "TSH-001",
    stock: 50,
    categoryId: "men-fashion-id",
    images: ["https://example.com/tshirt-1.jpg"],
    featured: true,
    status: "ACTIVE",
    tags: ["áo thun", "nam", "cotton", "basic"],
  },
};
```

### 3. Test Categories

```typescript
const testCategories = {
  fashion: {
    name: "Thời trang",
    description: "Danh mục thời trang và phụ kiện",
    image: "https://example.com/fashion.jpg",
  },
  menFashion: {
    name: "Thời trang nam",
    description: "Quần áo và phụ kiện dành cho nam giới",
    parentId: "fashion-id",
  },
};
```

## Helper Functions

### 1. Authentication Helpers

```typescript
// Đăng nhập với admin user
await loginAsAdmin(page, adminUsers.mainAdmin);

// Đăng xuất
await logoutFromAdmin(page);
```

### 2. Navigation Helpers

```typescript
// Navigate đến admin products
await navigateToAdminProducts(page);

// Navigate đến admin categories
await navigateToAdminCategories(page);
```

### 3. CRUD Helpers

```typescript
// Tạo sản phẩm qua UI
await createProductViaUI(page, testProducts.tshirt);

// Tạo danh mục qua UI
await createCategoryViaUI(page, testCategories.fashion);

// Xóa sản phẩm
await deleteProductViaUI(page, productName);

// Xóa danh mục
await deleteCategoryViaUI(page, categoryName);
```

### 4. Search và Verification Helpers

```typescript
// Tìm kiếm sản phẩm
await searchProducts(page, searchTerm);

// Tìm kiếm danh mục
await searchCategories(page, searchTerm);

// Verify sản phẩm tồn tại
await verifyProductExists(page, productName);

// Verify danh mục tồn tại
await verifyCategoryExists(page, categoryName);
```

## Test Scenarios Chi Tiết

### 1. Products Management

#### Tạo Sản Phẩm Mới

```typescript
test("should create new product successfully", async ({ page }) => {
  await createProductViaUI(page, testProducts.tshirt);

  // Verify redirect to products list
  await expect(page).toHaveURL("/admin/products");

  // Verify success message
  await expect(page.locator(".toast")).toContainText("Tạo sản phẩm thành công");

  // Verify product appears in list
  await verifyProductExists(page, testProducts.tshirt.name);
});
```

#### Validation Testing

```typescript
test("should validate required fields", async ({ page }) => {
  await page.goto("/admin/products/create");

  // Try to submit empty form
  await page.click('button[type="submit"]');

  // Verify validation messages
  await expect(page.locator("text=Tên sản phẩm là bắt buộc")).toBeVisible();
  await expect(page.locator("text=Giá sản phẩm là bắt buộc")).toBeVisible();
});
```

#### Bulk Operations

```typescript
test("should bulk delete multiple products", async ({ page }) => {
  // Create multiple products
  for (const product of bulkTestProducts) {
    await createProductViaUI(page, product);
  }

  // Perform bulk delete
  const productNames = bulkTestProducts.map((p) => p.name);
  await bulkDeleteProducts(page, productNames);

  // Verify all products are deleted
  for (const productName of productNames) {
    const productRow = page.locator(`tr:has-text("${productName}")`);
    await expect(productRow).not.toBeVisible();
  }
});
```

### 2. Categories Management

#### Tạo Danh Mục Phân Cấp

```typescript
test("should create child category with parent selection", async ({ page }) => {
  // Create parent category first
  await createCategoryViaUI(page, testCategories.fashion);

  // Create child category
  await page.goto("/admin/categories/create");
  await page.fill(
    'input[placeholder="Nhập tên danh mục"]',
    testCategories.menFashion.name
  );

  // Select parent category
  const parentSelect = page.locator('select[name="parentId"]');
  await parentSelect.selectOption({ label: testCategories.fashion.name });

  await page.click('button[type="submit"]');

  // Verify success
  await expect(page.locator(".toast")).toContainText("Tạo danh mục thành công");
});
```

#### Kiểm Tra Ràng Buộc Xóa

```typescript
test("should prevent deletion of category with products", async ({ page }) => {
  // Category with products should have disabled delete button
  const categoryWithProducts = page.locator(
    '[data-testid="category-with-products"]'
  );
  const deleteButton = categoryWithProducts.locator(
    'button[data-testid="delete-button"]'
  );
  await expect(deleteButton).toBeDisabled();

  // Verify tooltip message
  await deleteButton.hover();
  await expect(
    page.locator("text=Không thể xóa danh mục có sản phẩm")
  ).toBeVisible();
});
```

## Error Handling Testing

### 1. Network Errors

```typescript
test("should handle network errors gracefully", async ({ page }) => {
  // Simulate network failure
  await page.route("**/api/admin/products", (route) => route.abort());

  await page.reload();

  // Verify error message
  await expect(page.locator("text=Lỗi tải dữ liệu")).toBeVisible();
  await expect(page.locator('button:has-text("Thử lại")')).toBeVisible();
});
```

### 2. Server Errors

```typescript
test("should handle server errors during operations", async ({ page }) => {
  // Simulate server error
  await page.route("**/api/admin/products", (route) => {
    route.fulfill({
      status: 500,
      contentType: "application/json",
      body: JSON.stringify({ error: "Lỗi máy chủ nội bộ" }),
    });
  });

  // Try to create product
  await page.goto("/admin/products/create");
  // ... fill form and submit

  // Verify error message
  await expect(page.locator(".toast")).toContainText("Lỗi máy chủ nội bộ");
});
```

## Best Practices

### 1. Test Organization

- Nhóm tests theo chức năng (Create, Read, Update, Delete)
- Sử dụng describe blocks để tổ chức tests
- Đặt tên test cases rõ ràng và mô tả chức năng

### 2. Test Data Management

- Sử dụng fixtures cho test data
- Tạo test data độc lập cho mỗi test
- Clean up data sau mỗi test nếu cần

### 3. Assertions

- Verify cả UI state và data state
- Kiểm tra success/error messages
- Verify navigation và redirects

### 4. Error Scenarios

- Test validation errors
- Test network failures
- Test server errors
- Test edge cases

## Troubleshooting

### 1. Tests Failing

- Kiểm tra server có đang chạy không
- Verify test data setup
- Check for timing issues (sử dụng waitFor)
- Review error messages trong test output

### 2. Flaky Tests

- Thêm explicit waits
- Sử dụng waitForLoadState
- Verify element visibility trước khi interact

### 3. Performance Issues

- Sử dụng parallel execution cẩn thận
- Optimize test data setup
- Consider using API calls cho data setup thay vì UI

## Test Coverage

### 1. Products Testing Coverage

- ✅ Hiển thị danh sách sản phẩm
- ✅ Tạo sản phẩm mới với validation
- ✅ Chỉnh sửa sản phẩm
- ✅ Xóa sản phẩm (đơn lẻ và hàng loạt)
- ✅ Tìm kiếm và lọc sản phẩm
- ✅ Phân trang và sắp xếp
- ✅ Quản lý hình ảnh và tags
- ✅ Quản lý trạng thái sản phẩm
- ✅ Error handling và network failures

### 2. Categories Testing Coverage

- ✅ Hiển thị danh sách danh mục phân cấp
- ✅ Tạo danh mục với parent-child relationship
- ✅ Chỉnh sửa danh mục
- ✅ Xóa danh mục với ràng buộc
- ✅ Tìm kiếm danh mục
- ✅ Quản lý cây phân cấp
- ✅ Hiển thị thống kê
- ✅ Error handling và validation

## Continuous Integration

### 1. GitHub Actions Setup

```yaml
name: E2E Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: "18"
      - run: npm ci
      - run: npx playwright install
      - run: npm run test:e2e
```

### 2. Test Reports

- HTML reports được generate tự động
- Screenshots cho failed tests
- Video recordings cho debugging

## Kết Luận

Testing admin products và categories là một phần quan trọng để đảm bảo chất lượng của NS Shop. Bằng cách sử dụng Playwright và các helper functions được cung cấp, chúng ta có thể tạo ra các test cases comprehensive và maintainable.

### Lợi Ích Của Testing Suite Này:

1. **Comprehensive Coverage**: Bao phủm tất cả chức năng chính
2. **Maintainable**: Sử dụng helper functions và fixtures
3. **Reliable**: Error handling và retry mechanisms
4. **Scalable**: Dễ dàng thêm test cases mới
5. **Documentation**: Tài liệu chi tiết bằng tiếng Việt

### Next Steps:

1. Chạy tests thường xuyên trong development
2. Integrate vào CI/CD pipeline
3. Monitor test results và fix flaky tests
4. Cập nhật tests khi có feature mới

Hãy luôn cập nhật tests khi có thay đổi về UI hoặc business logic, và đảm bảo tất cả tests đều pass trước khi deploy lên production.
